#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <string>
#include <wininet.h>
#include <urlmon.h>
#include <clocale>
#include <algorithm>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// 前向声明
bool IsTextFunction(const char* functionName);
std::string ConvertToAnsi(const char* input, int length);
std::string ExtractTextFromPointer(int pointerValue);
int SafeGetStringLength(const char* str, int maxLen);

// 全局变量
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;
static char g_DebugInfo[1024] = {0}; // 调试信息

// 字符串函数列表 - 专门添加您的文本测试函数名
static const char* TEXT_FUNCTIONS[] = {
    "文本测试",      // 您的函数
    "文本",          // 包含文本的函数
    "Text",
    "String",
    "GetText",
    "GetString",
    "Version",
    "测试",          // 测试函数
    NULL            // 结束标记
};

// 检查是否为文本函数
bool IsTextFunction(const char* functionName) {
    if (!functionName) return false;

    // 检查预定义的文本函数列表
    for (int i = 0; TEXT_FUNCTIONS[i] != NULL; i++) {
        if (strcmp(functionName, TEXT_FUNCTIONS[i]) == 0) {
            return true;
        }
    }

    // 检查函数名是否包含关键词
    if (strstr(functionName, "文本") ||
        strstr(functionName, "Text") ||
        strstr(functionName, "String") ||
        strstr(functionName, "Get") ||
        strstr(functionName, "Version")) {
        return true;
    }

    return false;
}

// 安全获取字符串长度的辅助函数
int SafeGetStringLength(const char* str, int maxLen) {
    if (!str) return 0;

    __try {
        return strnlen(str, maxLen);
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        return 0;
    }
}

// 简化的编码转换函数 - 避免SEH冲突
std::string ConvertToAnsi(const char* input, int length) {
    if (!input || length <= 0) {
        return "";
    }

    // 简单的长度检查
    int actualLength = SafeGetStringLength(input, (length < 1024) ? length : 1024);
    if (actualLength == 0) {
        return "";
    }

    // 直接使用输入（假设已经是正确编码）
    return std::string(input, actualLength);
}

// 简化的字符串指针处理函数 - 避免SEH冲突
std::string ExtractTextFromPointer(int pointerValue) {
    // 将数字视为指针
    const char* strPtr = reinterpret_cast<const char*>(pointerValue);

    // 使用安全函数获取长度
    int length = SafeGetStringLength(strPtr, 1024);

    if (length > 0) {
        return ConvertToAnsi(strPtr, length);
    }

    // 返回指针值的字符串表示
    char buffer[64];
    sprintf_s(buffer, "Pointer: %p", strPtr);
    return buffer;
}

// 主要的RemoteCallDLL函数
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName, const char* parameters, int paramCount, void* result, int resultType, int bufferSize)
{
    if (!remotePath || !functionName || !result) {
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;

    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());

        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5) {
                strcpy_s((char*)result, bufferSize, "Download failed");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }

    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "Load failed");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "Function not found");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    int functionResult = 0;
    std::string textResult;
    bool isTextFunction = IsTextFunction(functionName);

    // 🎯 调试信息
    sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
        "Function: %s, ParamCount: %d, ResultType: %d, BufferSize: %d, IsTextFunc: %s",
        functionName, paramCount, resultType, bufferSize, isTextFunction ? "Yes" : "No");

    // 🎯 强制将所有函数都当作可能返回文本的函数处理
    // 这样确保 resultType=3 时总是能得到文本结果

    // 首先尝试作为文本函数调用
    bool textCallSuccess = false;
    if (paramCount == 0) {
        typedef const char* (__stdcall *StringFunc0)();
        StringFunc0 func = (StringFunc0)procAddr;
        const char* rawResult = func();

        if (rawResult) {
            int rawLen = SafeGetStringLength(rawResult, 1024);
            if (rawLen > 0) {
                textResult = ConvertToAnsi(rawResult, rawLen);
                functionResult = (int)rawResult;
                textCallSuccess = true;
            }
        }
    } else if (paramCount == 1) {
        typedef const char* (__stdcall *StringFunc1)(int);
        StringFunc1 func = (StringFunc1)procAddr;
        int param = parameters ? atoi(parameters) : 0;
        const char* rawResult = func(param);

        if (rawResult) {
            int rawLen = SafeGetStringLength(rawResult, 1024);
            if (rawLen > 0) {
                textResult = ConvertToAnsi(rawResult, rawLen);
                functionResult = (int)rawResult;
                textCallSuccess = true;
            }
        }
    }

    // 如果文本调用失败，尝试作为整数函数调用
    if (!textCallSuccess) {
        if (paramCount == 0) {
            typedef int (__stdcall *IntFunc0)();
            IntFunc0 func = (IntFunc0)procAddr;
            functionResult = func();
        } else if (paramCount == 1) {
            typedef int (__stdcall *IntFunc1)(int);
            IntFunc1 func = (IntFunc1)procAddr;
            int param = parameters ? atoi(parameters) : 0;
            functionResult = func(param);
        } else if (paramCount == 2) {
            typedef int (__stdcall *IntFunc2)(int, int);
            IntFunc2 func = (IntFunc2)procAddr;

            int param1 = 0, param2 = 0;
            if (parameters) {
                char paramsCopy[256];
                strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);
                char* comma = strchr(paramsCopy, ',');
                if (comma) {
                    *comma = '\0';
                    param1 = atoi(paramsCopy);
                    param2 = atoi(comma + 1);
                } else {
                    param1 = atoi(paramsCopy);
                }
            }
            functionResult = func(param1, param2);
        } else {
            functionResult = -1;
        }

        // 如果是文本函数但文本调用失败，尝试从整数结果提取文本
        if (isTextFunction) {
            textResult = ExtractTextFromPointer(functionResult);
            if (!textResult.empty() && textResult.find("Pointer:") == std::string::npos) {
                textCallSuccess = true;
            }
        }
    }

    // 🎯 统一的结果处理 - 确保 resultType=3 总是返回文本
    switch (resultType) {
        case 1: // 整数
            *(int*)result = functionResult;
            break;
        case 2: // 浮点
            *(double*)result = (double)functionResult;
            break;
        case 3: // 字符串 - 这是关键！
        case 5: // 自动
            {
                if (bufferSize > 0) {
                    if (textCallSuccess && !textResult.empty()) {
                        // 有文本结果，直接使用
                        int textLen = (int)textResult.length();
                        int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
                        if (copyLen > 0) {
                            memcpy((char*)result, textResult.c_str(), copyLen);
                        }
                        ((char*)result)[copyLen] = '\0';
                    } else {
                        // 没有文本结果，将整数转换为字符串
                        sprintf_s((char*)result, bufferSize, "%d", functionResult);
                    }
                } else if (bufferSize > 0) {
                    ((char*)result)[0] = '\0';
                }
            }
            break;
        case 4: // 逻辑
            *(BOOL*)result = (functionResult != 0);
            break;
        case 6: // 长整数
            *(long long*)result = (long long)functionResult;
            break;
    }

    // 存储到全局变量
    if (textCallSuccess && !textResult.empty()) {
        strncpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult.c_str(), _TRUNCATE);
        g_LastCallResult = functionResult;
        g_LastCallResultType = 2; // 文本类型
    } else {
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", functionResult);
        g_LastCallResult = functionResult;
        g_LastCallResultType = 0; // 数值类型
    }

    g_LastCallResultDouble = (double)functionResult;
    g_LastCallHasResult = TRUE;

    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);

    return 1;
}

// 其他函数
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    return 1;
}

MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    return 0;
}

MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

// 特殊添加：从指针提取文本
MYDLL_API int __stdcall ExtractTextFromResultInt(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0 || !g_LastCallHasResult) return 0;

    // 获取上次调用的整数结果（可能是指针）
    int ptrValue = g_LastCallResult;

    // 从指针中提取字符串
    std::string extractedText = ExtractTextFromPointer(ptrValue);

    // 复制到缓冲区
    int textLen = (int)extractedText.length();
    int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
    if (copyLen > 0) {
        memcpy(buffer, extractedText.c_str(), copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    // 对于文本函数，尝试提取实际文本内容
    if (g_LastCallResultType == 2) {
        int len = strlen(g_LastCallResultString);
        int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

        if (copyLen > 0) {
            memcpy(buffer, g_LastCallResultString, copyLen);
        }
        buffer[copyLen] = '\0';

        return copyLen;
    } else {
        // 数值结果 - 尝试将指针转换为字符串
        return ExtractTextFromResultInt(buffer, bufferSize);
    }
}

MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

// 🎯 新增：获取调试信息
MYDLL_API int __stdcall GetDebugInfo(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    int len = strlen(g_DebugInfo);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

    if (copyLen > 0) {
        memcpy(buffer, g_DebugInfo, copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        std::setlocale(LC_ALL, "C");
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        break;
    }
    return TRUE;
}
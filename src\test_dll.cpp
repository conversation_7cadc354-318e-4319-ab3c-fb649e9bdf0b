#include <windows.h>
#include <string>

// 测试用的文本函数
extern "C" __declspec(dllexport) const char* __stdcall 文本测试() {
    static std::string result = "这是文本测试的返回结果！";
    return result.c_str();
}

// 测试用的整数函数
extern "C" __declspec(dllexport) int __stdcall 整数测试() {
    return 12345;
}

// 带参数的文本函数
extern "C" __declspec(dllexport) const char* __stdcall 文本测试带参数(int param) {
    static char buffer[256];
    sprintf_s(buffer, "参数值是: %d", param);
    return buffer;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    return TRUE;
}

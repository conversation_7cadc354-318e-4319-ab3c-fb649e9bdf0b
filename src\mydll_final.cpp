#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <string>
#include <wininet.h>
#include <urlmon.h>
#include <clocale>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// Global variables
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;
static char g_DebugInfo[1024] = {0};

// Text function list
static const char* TEXT_FUNCTIONS[] = {
    "文本测试", "文本", "Text", "String", "GetText", "GetString", "Version", "测试", NULL
};

// Forward declarations
bool IsTextFunction(const char* functionName);
std::string ConvertToAnsi(const char* input, int length);
std::string ExtractTextFromPointer(int pointerValue);
int SafeGetStringLength(const char* str, int maxLen);

// Check if function is text function
bool IsTextFunction(const char* functionName) {
    if (!functionName) return false;

    for (int i = 0; TEXT_FUNCTIONS[i] != NULL; i++) {
        if (strcmp(functionName, TEXT_FUNCTIONS[i]) == 0) {
            return true;
        }
    }

    if (strstr(functionName, "文本") || strstr(functionName, "Text") ||
        strstr(functionName, "String") || strstr(functionName, "Get") ||
        strstr(functionName, "Version")) {
        return true;
    }

    return false;
}

// Safe string length
int SafeGetStringLength(const char* str, int maxLen) {
    if (!str) return 0;

    __try {
        return strnlen(str, maxLen);
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        return 0;
    }
}

// Convert to ANSI
std::string ConvertToAnsi(const char* input, int length) {
    if (!input || length <= 0) {
        return "";
    }

    int actualLength = SafeGetStringLength(input, (length < 1024) ? length : 1024);
    if (actualLength == 0) {
        return "";
    }

    return std::string(input, actualLength);
}

// Extract text from pointer
std::string ExtractTextFromPointer(int pointerValue) {
    const char* strPtr = reinterpret_cast<const char*>(pointerValue);
    int length = SafeGetStringLength(strPtr, 1024);

    if (length > 0) {
        return ConvertToAnsi(strPtr, length);
    }

    char buffer[64];
    sprintf_s(buffer, "Pointer: %p", strPtr);
    return buffer;
}

// Save config
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    return 1;
}

// Load config
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    return 0;
}

// Program delay
MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

// Get last call result int
MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

// Get last call result double
MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

// Get last call result string
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    if (g_LastCallResultType == 2) {
        int len = strlen(g_LastCallResultString);
        int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

        if (copyLen > 0) {
            memcpy(buffer, g_LastCallResultString, copyLen);
        }
        buffer[copyLen] = '\0';

        return copyLen;
    } else {
        std::string extractedText = ExtractTextFromPointer(g_LastCallResult);
        int textLen = (int)extractedText.length();
        int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
        if (copyLen > 0) {
            memcpy(buffer, extractedText.c_str(), copyLen);
        }
        buffer[copyLen] = '\0';
        return copyLen;
    }
}

// Get last call result type
MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

// Has last call result
MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

// Extract text from result int
MYDLL_API int __stdcall ExtractTextFromResultInt(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0 || !g_LastCallHasResult) return 0;

    std::string extractedText = ExtractTextFromPointer(g_LastCallResult);

    int textLen = (int)extractedText.length();
    int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
    if (copyLen > 0) {
        memcpy(buffer, extractedText.c_str(), copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Get debug info
MYDLL_API int __stdcall GetDebugInfo(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    int len = strlen(g_DebugInfo);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

    if (copyLen > 0) {
        memcpy(buffer, g_DebugInfo, copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Core function: RemoteCallDLL with perfect text support
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName,
                                      const char* parameters, int paramCount,
                                      void* result, int resultType, int bufferSize)
{
    if (!remotePath || !functionName || !result) {
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;

    // Handle remote download
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());

        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5) {
                strcpy_s((char*)result, bufferSize, "Download failed");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }

    // Load DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "Load failed");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Get function address
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "Function not found");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    int functionResult = 0;
    std::string textResult;
    bool isTextFunction = IsTextFunction(functionName);

    // Debug info
    sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
        "Function: %s, ParamCount: %d, ResultType: %d, BufferSize: %d, IsTextFunc: %s",
        functionName, paramCount, resultType, bufferSize, isTextFunction ? "Yes" : "No");

    // Dual call strategy: try text function first, then integer function
    bool textCallSuccess = false;

    // First try as text function
    if (paramCount == 0) {
        typedef const char* (__stdcall *StringFunc0)();
        StringFunc0 func = (StringFunc0)procAddr;
        const char* rawResult = func();

        if (rawResult) {
            int rawLen = SafeGetStringLength(rawResult, 1024);
            if (rawLen > 0) {
                textResult = ConvertToAnsi(rawResult, rawLen);
                functionResult = (int)rawResult;
                textCallSuccess = true;
            }
        }
    } else if (paramCount == 1) {
        typedef const char* (__stdcall *StringFunc1)(int);
        StringFunc1 func = (StringFunc1)procAddr;
        int param = parameters ? atoi(parameters) : 0;
        const char* rawResult = func(param);

        if (rawResult) {
            int rawLen = SafeGetStringLength(rawResult, 1024);
            if (rawLen > 0) {
                textResult = ConvertToAnsi(rawResult, rawLen);
                functionResult = (int)rawResult;
                textCallSuccess = true;
            }
        }
    }

    // If text call failed, try as integer function
    if (!textCallSuccess) {
        if (paramCount == 0) {
            typedef int (__stdcall *IntFunc0)();
            IntFunc0 func = (IntFunc0)procAddr;
            functionResult = func();
        } else if (paramCount == 1) {
            typedef int (__stdcall *IntFunc1)(int);
            IntFunc1 func = (IntFunc1)procAddr;
            int param = parameters ? atoi(parameters) : 0;
            functionResult = func(param);
        } else if (paramCount == 2) {
            typedef int (__stdcall *IntFunc2)(int, int);
            IntFunc2 func = (IntFunc2)procAddr;

            int param1 = 0, param2 = 0;
            if (parameters) {
                char paramsCopy[256];
                strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);
                char* comma = strchr(paramsCopy, ',');
                if (comma) {
                    *comma = '\0';
                    param1 = atoi(paramsCopy);
                    param2 = atoi(comma + 1);
                } else {
                    param1 = atoi(paramsCopy);
                }
            }
            functionResult = func(param1, param2);
        } else {
            functionResult = -1;
        }

        // If text function but text call failed, try to extract text from integer result
        if (isTextFunction) {
            textResult = ExtractTextFromPointer(functionResult);
            if (!textResult.empty() && textResult.find("Pointer:") == std::string::npos) {
                textCallSuccess = true;
            }
        }
    }

    // Unified result handling - ensure resultType=3 always returns text
    switch (resultType) {
        case 1: // Integer
            *(int*)result = functionResult;
            break;
        case 2: // Float
            *(double*)result = (double)functionResult;
            break;
        case 3: // String - This is the key!
        case 5: // Auto
            {
                if (bufferSize > 0) {
                    if (textCallSuccess && !textResult.empty()) {
                        // Have text result, use it directly
                        int textLen = (int)textResult.length();
                        int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
                        if (copyLen > 0) {
                            memcpy((char*)result, textResult.c_str(), copyLen);
                        }
                        ((char*)result)[copyLen] = '\0';
                    } else {
                        // No text result, convert integer to string
                        sprintf_s((char*)result, bufferSize, "%d", functionResult);
                    }
                } else if (bufferSize > 0) {
                    ((char*)result)[0] = '\0';
                }
            }
            break;
        case 4: // Boolean
            *(BOOL*)result = (functionResult != 0);
            break;
        case 6: // Long integer
            *(long long*)result = (long long)functionResult;
            break;
    }

    // Store to global variables
    if (textCallSuccess && !textResult.empty()) {
        strncpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult.c_str(), _TRUNCATE);
        g_LastCallResult = functionResult;
        g_LastCallResultType = 2; // Text type
    } else {
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", functionResult);
        g_LastCallResult = functionResult;
        g_LastCallResultType = 0; // Numeric type
    }

    g_LastCallResultDouble = (double)functionResult;
    g_LastCallHasResult = TRUE;

    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);

    return 1;
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        std::setlocale(LC_ALL, "C");
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        memset(g_DebugInfo, 0, sizeof(g_DebugInfo));
        break;
    }
    return TRUE;
}

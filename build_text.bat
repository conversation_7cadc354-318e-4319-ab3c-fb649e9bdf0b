@echo off
echo 编译专门处理文本函数的DLL...

if not exist "build" mkdir build

echo 创建DEF文件...
echo LIBRARY MyDll > src\mydll.def
echo EXPORTS >> src\mydll.def
echo     SaveConfig @1 >> src\mydll.def
echo     LoadConfig @2 >> src\mydll.def
echo     ProgramDelay @3 >> src\mydll.def
echo     RemoteCallDLL @4 >> src\mydll.def
echo     GetLastCallResultInt @5 >> src\mydll.def
echo     GetLastCallResultDouble @6 >> src\mydll.def
echo     GetLastCallResultString @7 >> src\mydll.def
echo     GetLastCallResultType @8 >> src\mydll.def
echo     HasLastCallResult @9 >> src\mydll.def
echo     ExtractTextFromResultInt @10 >> src\mydll.def

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat"

echo 编译专门文本处理版本...
cl /LD /Fe:build\MyDll.dll src\text_handler.cpp src\mydll.def /DMYDLL_EXPORTS /O2 /MT /EHsc

if errorlevel 1 (
    echo 编译失败
) else (
    echo 编译成功!
    copy build\MyDll.dll . >nul 2>&1
    echo 文本处理版本DLL已准备就绪!
)

pause 
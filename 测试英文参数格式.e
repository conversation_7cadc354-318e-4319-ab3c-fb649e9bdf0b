.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为您的实际DLL地址

调试输出 ("=== 英文参数格式测试 ===")
调试输出 ("🚀 修复编译问题，使用英文关键词")
调试输出 ("✅ 避免中文字符编码问题")
调试输出 ("✅ 保持所有功能不变")
调试输出 ("✅ 更好的兼容性")
调试输出 ("")

' 演示英文格式
演示英文格式 ()

' 对比中英文格式
对比格式 ()

.子程序 演示英文格式

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 英文参数格式演示 ===")

' 示例1：基础类型
调试输出 ("1. 基础类型组合")
结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "str:Hello,int:123,float:3.14", 0, 结果, 5, 1024)

调试输出 ("  参数格式: \"str:Hello,int:123,float:3.14\"")
调试输出 ("  调用成功: " + 到文本 (调用成功))
调试输出 ("  结果: [" + 结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("  调试信息: " + 调试信息)
调试输出 ("")

' 示例2：逻辑型和长整数型
调试输出 ("2. 逻辑型和长整数型")
结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "bool:true,long:9999999999", 0, 结果, 5, 1024)

调试输出 ("  参数格式: \"bool:true,long:9999999999\"")
调试输出 ("  调用成功: " + 到文本 (调用成功))
调试输出 ("  结果: [" + 结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("  调试信息: " + 调试信息)
调试输出 ("")

' 示例3：高级类型
调试输出 ("3. 高级类型")
结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "byte:255,short:32767,double:3.141592653589793", 0, 结果, 5, 1024)

调试输出 ("  参数格式: \"byte:255,short:32767,double:3.141592653589793\"")
调试输出 ("  调用成功: " + 到文本 (调用成功))
调试输出 ("  结果: [" + 结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("  调试信息: " + 调试信息)
调试输出 ("")

' 示例4：特殊类型
调试输出 ("4. 特殊类型")
结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "bytes:48656C6C6F,date:2024-01-01,func:callback", 0, 结果, 5, 1024)

调试输出 ("  参数格式: \"bytes:48656C6C6F,date:2024-01-01,func:callback\"")
调试输出 ("  调用成功: " + 到文本 (调用成功))
调试输出 ("  结果: [" + 结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("  调试信息: " + 调试信息)
调试输出 ("")

.子程序 对比格式

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 格式对比 ===")

' 旧格式（向后兼容）
调试输出 ("1. 旧格式（逗号分隔）")
结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "99,98,Hello", 3, 结果, 5, 1024)

调试输出 ("  参数: \"99,98,Hello\"")
调试输出 ("  特点: 系统自动猜测类型")
调试输出 ("  结果: [" + 结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("  系统猜测: " + 调试信息)
调试输出 ("")

' 新格式（英文关键词）
调试输出 ("2. 新格式（英文关键词）")
结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "int:99,int:98,str:Hello", 0, 结果, 5, 1024)

调试输出 ("  参数: \"int:99,int:98,str:Hello\"")
调试输出 ("  特点: 明确指定每个参数类型")
调试输出 ("  结果: [" + 结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("  精确解析: " + 调试信息)
调试输出 ("")

.子程序 测试所有类型

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试所有支持的类型 ===")

' 测试每种类型
.局部变量 类型列表, 文本型, , "11"
.局部变量 i, 整数型

类型列表 [1] = "str:Hello"                    ' 字符串
类型列表 [2] = "int:123"                      ' 整数
类型列表 [3] = "float:3.14"                   ' 浮点数
类型列表 [4] = "double:3.141592653589793"     ' 双精度
类型列表 [5] = "bool:true"                    ' 逻辑型
类型列表 [6] = "byte:255"                     ' 字节型
类型列表 [7] = "short:32767"                  ' 短整数
类型列表 [8] = "long:9999999999"              ' 长整数
类型列表 [9] = "bytes:48656C6C6F"             ' 字节集
类型列表 [10] = "date:2024-01-01"             ' 日期时间
类型列表 [11] = "func:callback_function"      ' 函数指针

计次循环首 (11, i)
    调试输出 ("测试类型 " + 到文本 (i) + ": " + 类型列表 [i])

    结果 = ""
    调用成功 = 远程调用DLL (地址, "测试", 类型列表 [i], 0, 结果, 5, 1024)

    调试输出 ("  调用成功: " + 到文本 (调用成功))
    调试输出 ("  结果: [" + 结果 + "]")

    获取调试信息 (调试信息, 2048)
    ' 提取参数类型信息
    .局部变量 类型位置, 整数型
    类型位置 = 寻找文本 (调试信息, "ParamTypes:", , 假)
    如果 (类型位置 > 0)
        .局部变量 类型信息, 文本型
        类型信息 = 取文本中间 (调试信息, 类型位置 + 11, 20)
        调试输出 ("  识别类型: " + 类型信息)
    如果真结束

    调试输出 ("")
计次循环尾 ()

调试输出 ("=== 总结 ===")
调试输出 ("🎯 英文格式的优势:")
调试输出 ("1. 避免中文字符编码问题")
调试输出 ("2. 更好的跨平台兼容性")
调试输出 ("3. 编译器友好，无警告")
调试输出 ("4. 保持所有功能不变")
调试输出 ("")
调试输出 ("📋 支持的英文关键词:")
调试输出 ("  str:     → 字符串类型")
调试输出 ("  int:     → 整数类型")
调试输出 ("  float:   → 浮点类型")
调试输出 ("  double:  → 双精度类型")
调试输出 ("  bool:    → 逻辑类型")
调试输出 ("  byte:    → 字节类型")
调试输出 ("  short:   → 短整数类型")
调试输出 ("  long:    → 长整数类型")
调试输出 ("  bytes:   → 字节集类型")
调试输出 ("  date:    → 日期时间类型")
调试输出 ("  func:    → 函数指针类型")
调试输出 ("")
调试输出 ("💡 使用建议:")
调试输出 ("1. 优先使用英文格式避免编码问题")
调试输出 ("2. 旧的逗号格式仍然完全支持")
调试输出 ("3. 可以混合使用两种格式")
调试输出 ("4. resultType=5 自动识别最佳返回类型")

.子程序 演示混合使用

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型

调试输出 ("")
调试输出 ("=== 演示混合使用 ===")

' 在同一个程序中混合使用不同格式
调试输出 ("1. 使用英文格式")
结果 = ""
调用成功 = 远程调用DLL (地址, "函数1", "str:Test,int:100", 0, 结果, 5, 1024)
调试输出 ("  英文格式结果: [" + 结果 + "]")

调试输出 ("2. 使用旧格式")
结果 = ""
调用成功 = 远程调用DLL (地址, "函数2", "Test,100", 2, 结果, 5, 1024)
调试输出 ("  旧格式结果: [" + 结果 + "]")

调试输出 ("3. 无参数调用")
结果 = ""
调用成功 = 远程调用DLL (地址, "GetVersion", "", 0, 结果, 5, 1024)
调试输出 ("  无参数结果: [" + 结果 + "]")

调试输出 ("")
调试输出 ("✅ 所有格式都能正常工作！")

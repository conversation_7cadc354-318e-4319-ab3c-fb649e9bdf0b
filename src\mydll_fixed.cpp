#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <string>
#include <wininet.h>
#include <urlmon.h>
#include <clocale>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// Global variables
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;
static char g_DebugInfo[1024] = {0};

// Text function list
static const char* TEXT_FUNCTIONS[] = {
    "文本测试", "文本", "Text", "String", "GetText", "GetString", "Version", "测试", NULL
};

// Forward declarations
bool IsTextFunction(const char* functionName);
std::string ConvertToAnsi(const char* input, int length);
std::string ExtractTextFromPointer(int pointerValue);
int SafeGetStringLength(const char* str, int maxLen);

// Check if function is text function
bool IsTextFunction(const char* functionName) {
    if (!functionName) return false;

    for (int i = 0; TEXT_FUNCTIONS[i] != NULL; i++) {
        if (strcmp(functionName, TEXT_FUNCTIONS[i]) == 0) {
            return true;
        }
    }

    if (strstr(functionName, "文本") || strstr(functionName, "Text") ||
        strstr(functionName, "String") || strstr(functionName, "Get") ||
        strstr(functionName, "Version")) {
        return true;
    }

    return false;
}

// Safe string length
int SafeGetStringLength(const char* str, int maxLen) {
    if (!str) return 0;

    __try {
        return strnlen(str, maxLen);
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        return 0;
    }
}

// Convert to ANSI
std::string ConvertToAnsi(const char* input, int length) {
    if (!input || length <= 0) {
        return "";
    }

    int actualLength = SafeGetStringLength(input, (length < 1024) ? length : 1024);
    if (actualLength == 0) {
        return "";
    }

    return std::string(input, actualLength);
}

// Extract text from pointer
std::string ExtractTextFromPointer(int pointerValue) {
    const char* strPtr = reinterpret_cast<const char*>(pointerValue);
    int length = SafeGetStringLength(strPtr, 1024);

    if (length > 0) {
        return ConvertToAnsi(strPtr, length);
    }

    char buffer[64];
    sprintf_s(buffer, "Pointer: %p", strPtr);
    return buffer;
}

// Save config
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    return 1;
}

// Load config
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    return 0;
}

// Program delay
MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

// Get last call result int
MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

// Get last call result double
MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

// Get last call result string
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    if (g_LastCallResultType == 2) {
        int len = strlen(g_LastCallResultString);
        int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

        if (copyLen > 0) {
            memcpy(buffer, g_LastCallResultString, copyLen);
        }
        buffer[copyLen] = '\0';

        return copyLen;
    } else {
        std::string extractedText = ExtractTextFromPointer(g_LastCallResult);
        int textLen = (int)extractedText.length();
        int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
        if (copyLen > 0) {
            memcpy(buffer, extractedText.c_str(), copyLen);
        }
        buffer[copyLen] = '\0';
        return copyLen;
    }
}

// Get last call result type
MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

// Has last call result
MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

// Extract text from result int
MYDLL_API int __stdcall ExtractTextFromResultInt(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0 || !g_LastCallHasResult) return 0;

    std::string extractedText = ExtractTextFromPointer(g_LastCallResult);

    int textLen = (int)extractedText.length();
    int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
    if (copyLen > 0) {
        memcpy(buffer, extractedText.c_str(), copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Get debug info
MYDLL_API int __stdcall GetDebugInfo(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    int len = strlen(g_DebugInfo);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

    if (copyLen > 0) {
        memcpy(buffer, g_DebugInfo, copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Universal Dynamic Call Engine - Safe and Stable
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName,
                                      const char* parameters, int paramCount,
                                      void* result, int resultType, int bufferSize)
{
    // Initialize result to safe defaults
    __try {
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                memset(result, 0, bufferSize);
            }
        } else {
            memset(result, 0, sizeof(int));
        }
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        return 0;
    }

    if (!remotePath || !functionName || !result) {
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Invalid parameters: remotePath=%p, functionName=%p, result=%p",
                  remotePath, functionName, result);
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;

    __try {
        // Handle remote download
        if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
            char tempDir[MAX_PATH];
            GetTempPathA(MAX_PATH, tempDir);
            sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());

            HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
            if (FAILED(hr)) {
                if (resultType == 3 || resultType == 5 && bufferSize > 0) {
                    strcpy_s((char*)result, bufferSize, "Download failed");
                } else {
                    *(int*)result = -1;
                }
                strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
                g_LastCallResult = -1;
                g_LastCallHasResult = TRUE;
                sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Download failed: %s", remotePath);
                return 0;
            }
            needCleanup = TRUE;
        } else {
            strcpy_s(dllPath, MAX_PATH, remotePath);
        }

        // Load DLL with error handling
        HMODULE hDll = LoadLibraryA(dllPath);
        if (!hDll) {
            DWORD error = GetLastError();
            if (resultType == 3 || resultType == 5 && bufferSize > 0) {
                sprintf_s((char*)result, bufferSize, "Load failed: %d", error);
            } else {
                *(int*)result = -1;
            }
            sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed: %d", error);
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "LoadLibrary failed: %s, Error: %d", dllPath, error);
            if (needCleanup) DeleteFileA(dllPath);
            return 0;
        }

        // Get function address
        FARPROC procAddr = GetProcAddress(hDll, functionName);
        if (!procAddr) {
            DWORD error = GetLastError();
            if (resultType == 3 || resultType == 5 && bufferSize > 0) {
                sprintf_s((char*)result, bufferSize, "Function not found: %s", functionName);
            } else {
                *(int*)result = -1;
            }
            sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found: %s", functionName);
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "GetProcAddress failed: %s, Error: %d", functionName, error);
            FreeLibrary(hDll);
            if (needCleanup) DeleteFileA(dllPath);
            return 0;
        }

        // Universal Dynamic Call Engine - Safe Parameter Handling
        int functionResult = 0;
        std::string textResult;
        bool callSuccess = false;

        // Parse parameters safely
        DWORD_PTR args[16] = {0}; // Support up to 16 parameters
        int actualParamCount = 0;

        if (parameters && strlen(parameters) > 0 && paramCount > 0) {
            char paramsCopy[1024];
            strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);

            char* token = strtok(paramsCopy, ",");
            while (token && actualParamCount < 16 && actualParamCount < paramCount) {
                // Trim spaces
                while (*token == ' ') token++;
                char* end = token + strlen(token) - 1;
                while (end > token && *end == ' ') *end-- = '\0';

                // Convert parameter to DWORD_PTR
                if (strlen(token) > 0) {
                    // Try to convert to number first
                    char* endptr;
                    long longVal = strtol(token, &endptr, 10);
                    if (*endptr == '\0') {
                        // It's a number
                        args[actualParamCount] = (DWORD_PTR)longVal;
                    } else {
                        // It's a string, store pointer
                        args[actualParamCount] = (DWORD_PTR)token;
                    }
                    actualParamCount++;
                }
                token = strtok(NULL, ",");
            }
        }

        // Enhanced debug info with parameter details
        char paramDetails[512] = {0};
        if (actualParamCount > 0) {
            sprintf_s(paramDetails, sizeof(paramDetails), "Parsed %d params: ", actualParamCount);
            for (int i = 0; i < actualParamCount && i < 5; i++) {
                char temp[64];
                sprintf_s(temp, sizeof(temp), "[%d]=0x%p ", i, (void*)args[i]);
                strcat_s(paramDetails, sizeof(paramDetails), temp);
            }
        }

        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Function: %s, ParamCount: %d->%d, ResultType: %d, BufferSize: %d, RawParams: [%s], %s",
            functionName, paramCount, actualParamCount, resultType, bufferSize,
            parameters ? parameters : "NULL", paramDetails);

        // Enhanced Universal Function Call - Multiple Calling Conventions
        int attemptCount = 0;
        char callDetails[256] = {0};

        // Try different calling conventions and parameter interpretations
        for (int attempt = 0; attempt < 3 && !callSuccess; attempt++) {
            attemptCount++;
            __try {
                switch (attempt) {
                    case 0: // Standard __stdcall with current args
                        {
                            switch (actualParamCount) {
                                case 0: {
                                    typedef DWORD_PTR (__stdcall *Func0)();
                                    Func0 func = (Func0)procAddr;
                                    functionResult = (int)func();
                                    callSuccess = true;
                                    strcpy_s(callDetails, "stdcall-0params");
                                    break;
                                }
                                case 1: {
                                    typedef DWORD_PTR (__stdcall *Func1)(DWORD_PTR);
                                    Func1 func = (Func1)procAddr;
                                    functionResult = (int)func(args[0]);
                                    callSuccess = true;
                                    sprintf_s(callDetails, "stdcall-1param(0x%p)", (void*)args[0]);
                                    break;
                                }
                                case 2: {
                                    typedef DWORD_PTR (__stdcall *Func2)(DWORD_PTR, DWORD_PTR);
                                    Func2 func = (Func2)procAddr;
                                    functionResult = (int)func(args[0], args[1]);
                                    callSuccess = true;
                                    sprintf_s(callDetails, "stdcall-2params(0x%p,0x%p)", (void*)args[0], (void*)args[1]);
                                    break;
                                }
                                default: {
                                    if (actualParamCount <= 10) {
                                        typedef DWORD_PTR (__stdcall *FuncGeneric)(DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR,
                                                                                   DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR);
                                        FuncGeneric func = (FuncGeneric)procAddr;
                                        functionResult = (int)func(args[0], args[1], args[2], args[3], args[4],
                                                                   args[5], args[6], args[7], args[8], args[9]);
                                        callSuccess = true;
                                        sprintf_s(callDetails, "stdcall-%dparams", actualParamCount);
                                    }
                                    break;
                                }
                            }
                        }
                        break;

                    case 1: // Try __cdecl calling convention
                        {
                            if (actualParamCount == 1) {
                                typedef DWORD_PTR (__cdecl *CdeclFunc1)(DWORD_PTR);
                                CdeclFunc1 func = (CdeclFunc1)procAddr;
                                functionResult = (int)func(args[0]);
                                callSuccess = true;
                                sprintf_s(callDetails, "cdecl-1param(0x%p)", (void*)args[0]);
                            }
                        }
                        break;

                    case 2: // Try with string parameter interpretation
                        {
                            if (actualParamCount == 1 && parameters) {
                                // Try as string parameter
                                typedef DWORD_PTR (__stdcall *StringFunc)(const char*);
                                StringFunc func = (StringFunc)procAddr;
                                functionResult = (int)func(parameters);
                                callSuccess = true;
                                sprintf_s(callDetails, "string-param(\"%s\")", parameters);
                            }
                        }
                        break;
                }
            } __except(EXCEPTION_EXECUTE_HANDLER) {
                // Continue to next attempt
                sprintf_s(callDetails, "attempt-%d-failed(0x%X)", attempt, GetExceptionCode());
            }
        }

        // Update debug info with call details
        char finalDebug[1024];
        sprintf_s(finalDebug, sizeof(finalDebug),
            "%s, CallResult: %d, Attempts: %d, LastAttempt: %s, FuncResult: 0x%X",
            g_DebugInfo, callSuccess ? 1 : 0, attemptCount, callDetails, functionResult);
        strcpy_s(g_DebugInfo, sizeof(g_DebugInfo), finalDebug);

        // Try to extract text from result if it's a pointer
        if (callSuccess && functionResult != 0) {
            const char* strPtr = reinterpret_cast<const char*>(functionResult);
            int textLen = SafeGetStringLength(strPtr, 1024);
            if (textLen > 0) {
                textResult = ConvertToAnsi(strPtr, textLen);
            }
        }

            // Safe result processing
            switch (resultType) {
                case 1: // Integer
                    *(int*)result = functionResult;
                    break;
                case 2: // Double
                    *(double*)result = (double)functionResult;
                    g_LastCallResultDouble = (double)functionResult;
                    break;
                case 3: // String
                case 5: // Auto
                    {
                        if (bufferSize > 0) {
                            if (!textResult.empty()) {
                                // Have text result, use it directly
                                int textLen = (int)textResult.length();
                                int copyLen = (textLen < bufferSize - 1) ? textLen : bufferSize - 1;
                                if (copyLen > 0) {
                                    memcpy((char*)result, textResult.c_str(), copyLen);
                                }
                                ((char*)result)[copyLen] = '\0';
                            } else {
                                // No text result, convert number to string
                                sprintf_s((char*)result, bufferSize, "%d", functionResult);
                            }
                        }
                    }
                    break;
                case 4: // Boolean
                    *(BOOL*)result = (functionResult != 0);
                    break;
                case 6: // Long integer
                    *(long long*)result = (long long)functionResult;
                    break;
                default:
                    // Unknown type, default to integer
                    *(int*)result = functionResult;
                    break;
            }

            // Store to global variables
            if (!textResult.empty()) {
                strncpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult.c_str(), _TRUNCATE);
                g_LastCallResult = functionResult;
                g_LastCallResultType = 2; // Text type
            } else {
                sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", functionResult);
                g_LastCallResult = functionResult;
                g_LastCallResultType = 0; // Numeric type
            }

            g_LastCallResultDouble = (double)functionResult;
            g_LastCallHasResult = TRUE;

        } __except(EXCEPTION_EXECUTE_HANDLER) {
            // Exception in result processing
            if (resultType == 3 || resultType == 5) {
                if (bufferSize > 0) {
                    strcpy_s((char*)result, bufferSize, "Result processing failed");
                }
            } else {
                *(int*)result = -1;
            }
            sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
                "Result processing exception: %s, Exception: 0x%X",
                functionName, GetExceptionCode());
        }

        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);

        return callSuccess ? 1 : 0;

    } __except(EXCEPTION_EXECUTE_HANDLER) {
        // Global exception handler
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                strcpy_s((char*)result, bufferSize, "Critical error");
            }
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Critical exception in RemoteCallDLL: %s, Exception: 0x%X",
            functionName ? functionName : "NULL", GetExceptionCode());
        return 0;
    }
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        std::setlocale(LC_ALL, "C");
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        memset(g_DebugInfo, 0, sizeof(g_DebugInfo));
        break;
    }
    return TRUE;
}

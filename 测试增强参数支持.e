.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为实际的DLL地址

调试输出 ("=== 测试增强的参数支持 ===")
调试输出 ("")

' 测试您的具体问题
测试您的原始问题 ()

' 测试各种参数组合
测试单个字符串参数 ()
测试多个参数 ()
测试混合参数类型 ()

.子程序 测试您的原始问题

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试您的原始问题 ===")

' 您的原始调用：远程调用DLL("test.dll", "文本测试", "1", 1, 文本结果, 3, 4096)
调用成功 = 远程调用DLL (地址, "文本测试", "1", 1, 文本结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 文本结果 + "]")

' 获取详细调试信息
获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试单个字符串参数

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试单个字符串参数 ===")

' 测试传递字符串参数
调用成功 = 远程调用DLL (地址, "测试", "Hello", 1, 结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("结果: [" + 结果 + "]")

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试多个参数

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试多个参数 ===")

' 测试两个参数
调用成功 = 远程调用DLL (地址, "加法", "10,20", 2, 结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("结果: [" + 结果 + "]")

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

' 测试三个参数
调用成功 = 远程调用DLL (地址, "三参数函数", "1,2,3", 3, 结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("结果: [" + 结果 + "]")

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试混合参数类型

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试混合参数类型 ===")

' 测试字符串+数字混合
调用成功 = 远程调用DLL (地址, "混合函数", "文本,123", 2, 结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("结果: [" + 结果 + "]")

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

' 测试带空格的参数
调用成功 = 远程调用DLL (地址, "空格测试", "参数1, 参数2 , 参数3", 3, 结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("结果: [" + 结果 + "]")

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")
调试输出 ("=== 测试完成 ===")

.子程序 测试不同返回类型

.局部变量 整数结果, 整数型
.局部变量 浮点结果, 小数型
.局部变量 文本结果, 文本型
.局部变量 逻辑结果, 逻辑型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试不同返回类型 ===")

' 测试整数返回
调用成功 = 远程调用DLL (地址, "整数函数", "100", 1, 整数结果, 1, 0)
调试输出 ("整数结果: " + 到文本 (整数结果))

' 测试浮点返回
调用成功 = 远程调用DLL (地址, "浮点函数", "3.14", 1, 浮点结果, 2, 0)
调试输出 ("浮点结果: " + 到文本 (浮点结果))

' 测试文本返回
调用成功 = 远程调用DLL (地址, "文本函数", "测试", 1, 文本结果, 3, 4096)
调试输出 ("文本结果: [" + 文本结果 + "]")

' 测试逻辑返回
调用成功 = 远程调用DLL (地址, "逻辑函数", "1", 1, 逻辑结果, 4, 0)
调试输出 ("逻辑结果: " + 到文本 (逻辑结果))

调试输出 ("")

#define MYDLL_EXPORTS // 确保MYDLL_API被定义为导出

#include "mydll.h"
#include <windows.h>
#include <commctrl.h>
#include <string>
#include <fstream>
#include <sstream>
#include <map>
#include <vector>
#include <set>
#include <algorithm>
#include <cctype>
#include <locale>
#include <ctime>
#include <direct.h>
#include <psapi.h> // 用于EmptyWorkingSet
#include <tlhelp32.h> // 用于进程检测
#include <utility> // 用于std::pair和std::make_pair
#include <process.h>  // 用于_beginthreadex
#include <wininet.h>
#include <urlmon.h>
#include <shlwapi.h>
#include <stdio.h>

// 链接所需的库
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")
#pragma comment(lib, "shlwapi.lib")

// ===============================================
// 🎯 完善的编码处理系统 - 解决文本返回问题
// ===============================================

// 编码类型枚举
enum EncodingType {
    ENCODING_UNKNOWN = 0,
    ENCODING_ANSI = 1,      // GBK/GB2312 - 易语言默认
    ENCODING_UTF8 = 2,      // UTF-8 - 网络通用
    ENCODING_UTF16LE = 3,   // UTF-16 Little Endian - Windows Unicode
    ENCODING_UTF16BE = 4,   // UTF-16 Big Endian
    ENCODING_ASCII = 5      // ASCII - 纯英文
};

// 编码检测结构
struct EncodingInfo {
    EncodingType type;
    int confidence;  // 置信度 0-100
    bool hasBOM;     // 是否有BOM标记
};

// 🔍 编码检测函数
EncodingInfo DetectEncoding(const char* data, int length) {
    EncodingInfo info = {ENCODING_UNKNOWN, 0, false};
    
    if (!data || length <= 0) {
        return info;
    }
    
    // 检查BOM标记
    if (length >= 3) {
        // UTF-8 BOM: EF BB BF
        if ((unsigned char)data[0] == 0xEF && 
            (unsigned char)data[1] == 0xBB && 
            (unsigned char)data[2] == 0xBF) {
            info.type = ENCODING_UTF8;
            info.confidence = 100;
            info.hasBOM = true;
            return info;
        }
    }
    
    if (length >= 2) {
        // UTF-16 LE BOM: FF FE
        if ((unsigned char)data[0] == 0xFF && 
            (unsigned char)data[1] == 0xFE) {
            info.type = ENCODING_UTF16LE;
            info.confidence = 100;
            info.hasBOM = true;
            return info;
        }
        
        // UTF-16 BE BOM: FE FF
        if ((unsigned char)data[0] == 0xFE && 
            (unsigned char)data[1] == 0xFF) {
            info.type = ENCODING_UTF16BE;
            info.confidence = 100;
            info.hasBOM = true;
            return info;
        }
    }
    
    // 统计字符特征
    int asciiCount = 0;
    int chineseCount = 0;
    int utf8Count = 0;
    int nullCount = 0;
    
    for (int i = 0; i < length; i++) {
        unsigned char c = (unsigned char)data[i];
        
        if (c == 0) {
            nullCount++;
        } else if (c < 128) {
            asciiCount++;
        } else if (c >= 0xA1 && c <= 0xFE) {
            // 可能是GBK中文字符
            if (i + 1 < length) {
                unsigned char c2 = (unsigned char)data[i + 1];
                if (c2 >= 0xA1 && c2 <= 0xFE) {
                    chineseCount += 2;
                    i++; // 跳过下一个字节
                }
            }
        } else if (c >= 0xC0 && c <= 0xFD) {
            // 可能是UTF-8字符
            int utf8Bytes = 0;
            if (c >= 0xC0 && c <= 0xDF) utf8Bytes = 2;
            else if (c >= 0xE0 && c <= 0xEF) utf8Bytes = 3;
            else if (c >= 0xF0 && c <= 0xF7) utf8Bytes = 4;
            
            bool validUtf8 = true;
            for (int j = 1; j < utf8Bytes && i + j < length; j++) {
                unsigned char cb = (unsigned char)data[i + j];
                if (cb < 0x80 || cb > 0xBF) {
                    validUtf8 = false;
                    break;
                }
            }
            
            if (validUtf8) {
                utf8Count += utf8Bytes;
                i += utf8Bytes - 1;
            }
        }
    }
    
    // 根据统计结果判断编码
    if (nullCount > length / 4) {
        // 包含很多null字符，可能是UTF-16
        info.type = ENCODING_UTF16LE;
        info.confidence = 60;
    } else if (utf8Count > chineseCount && utf8Count > 0) {
        info.type = ENCODING_UTF8;
        info.confidence = 80;
    } else if (chineseCount > 0) {
        info.type = ENCODING_ANSI;
        info.confidence = 85;
    } else if (asciiCount == length) {
        info.type = ENCODING_ASCII;
        info.confidence = 95;
    } else {
        info.type = ENCODING_ANSI; // 默认为ANSI
        info.confidence = 50;
    }
    
    return info;
}

// 🔄 编码转换函数
std::string ConvertEncoding(const char* input, int inputLen, EncodingType fromEnc, EncodingType toEnc) {
    if (!input || inputLen <= 0 || fromEnc == toEnc) {
        return std::string(input, inputLen);
    }
    
    std::string result;
    
    try {
        // 转换为Unicode作为中间格式
        std::wstring unicode;
        
        // 第一步：转换为Unicode
        switch (fromEnc) {
            case ENCODING_ANSI:
                {
                    int wideLen = MultiByteToWideChar(CP_ACP, 0, input, inputLen, NULL, 0);
                    if (wideLen > 0) {
                        unicode.resize(wideLen);
                        MultiByteToWideChar(CP_ACP, 0, input, inputLen, &unicode[0], wideLen);
                    }
                }
                break;
                
            case ENCODING_UTF8:
                {
                    int wideLen = MultiByteToWideChar(CP_UTF8, 0, input, inputLen, NULL, 0);
                    if (wideLen > 0) {
                        unicode.resize(wideLen);
                        MultiByteToWideChar(CP_UTF8, 0, input, inputLen, &unicode[0], wideLen);
                    }
                }
                break;
                
            case ENCODING_UTF16LE:
                unicode = std::wstring((wchar_t*)input, inputLen / 2);
                break;
                
            default:
                // 默认按ANSI处理
                int wideLen = MultiByteToWideChar(CP_ACP, 0, input, inputLen, NULL, 0);
                if (wideLen > 0) {
                    unicode.resize(wideLen);
                    MultiByteToWideChar(CP_ACP, 0, input, inputLen, &unicode[0], wideLen);
                }
                break;
        }
        
        // 第二步：从Unicode转换为目标编码
        if (!unicode.empty()) {
            switch (toEnc) {
                case ENCODING_ANSI:
                    {
                        int mbLen = WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, NULL, 0, NULL, NULL);
                        if (mbLen > 0) {
                            result.resize(mbLen - 1); // 去掉null终止符
                            WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, &result[0], mbLen, NULL, NULL);
                        }
                    }
                    break;
                    
                case ENCODING_UTF8:
                    {
                        int mbLen = WideCharToMultiByte(CP_UTF8, 0, unicode.c_str(), -1, NULL, 0, NULL, NULL);
                        if (mbLen > 0) {
                            result.resize(mbLen - 1); // 去掉null终止符
                            WideCharToMultiByte(CP_UTF8, 0, unicode.c_str(), -1, &result[0], mbLen, NULL, NULL);
                        }
                    }
                    break;
                    
                case ENCODING_UTF16LE:
                    result = std::string((char*)unicode.c_str(), unicode.length() * 2);
                    break;
                    
                default:
                    // 默认转换为ANSI
                    int mbLen = WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, NULL, 0, NULL, NULL);
                    if (mbLen > 0) {
                        result.resize(mbLen - 1);
                        WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, &result[0], mbLen, NULL, NULL);
                    }
                    break;
            }
        }
    } catch (...) {
        // 转换失败，返回原始数据
        result = std::string(input, inputLen);
    }
    
    return result;
}

// 🎯 智能字符串处理函数 - 确保返回正确的文本
std::string SmartStringProcess(const char* rawData, int length) {
    if (!rawData || length <= 0) {
        return "空数据";
    }
    
    // 检测编码
    EncodingInfo encoding = DetectEncoding(rawData, length);
    
    // 转换为易语言兼容的ANSI编码
    std::string result = ConvertEncoding(rawData, length, encoding.type, ENCODING_ANSI);
    
    // 验证转换结果
    if (result.empty()) {
        // 转换失败，尝试其他方法
        
        // 尝试直接作为UTF-8处理
        result = ConvertEncoding(rawData, length, ENCODING_UTF8, ENCODING_ANSI);
        
        if (result.empty()) {
            // 仍然失败，尝试直接复制
            result = std::string(rawData, length);
        }
    }
    
    // 清理字符串：移除控制字符和无效字符
    std::string cleaned;
    for (size_t i = 0; i < result.length(); i++) {
        unsigned char c = (unsigned char)result[i];
        
        // 保留可打印字符和中文字符
        if ((c >= 32 && c <= 126) ||   // ASCII可打印字符
            (c >= 161 && c <= 254)) {   // 中文字符范围
            cleaned += c;
        } else if (c == '\r' || c == '\n' || c == '\t') {
            // 保留常见的格式字符
            cleaned += c;
        }
        // 跳过其他控制字符
    }
    
    // 如果清理后为空，返回错误信息
    if (cleaned.empty()) {
        return "无效文本数据";
    }
    
    // 移除首尾空白字符
    size_t start = cleaned.find_first_not_of(" \t\r\n");
    if (start == std::string::npos) {
        return "空白文本";
    }
    
    size_t end = cleaned.find_last_not_of(" \t\r\n");
    result = cleaned.substr(start, end - start + 1);
    
    return result;
}

// 🔧 字符串验证函数 - 确保是有效的文本
bool IsValidTextString(const char* str, int length) {
    if (!str || length <= 0) {
        return false;
    }
    
    // 检查是否包含可打印字符
    bool hasValidChar = false;
    for (int i = 0; i < length; i++) {
        unsigned char c = (unsigned char)str[i];
        if ((c >= 32 && c <= 126) || (c >= 161 && c <= 254)) {
            hasValidChar = true;
            break;
        }
    }
    
    return hasValidChar;
}

// 🎯 易语言文本格式化函数
std::string FormatForELanguage(const std::string& text) {
    if (text.empty()) {
        return "";
    }
    
    // 确保字符串以null结尾
    std::string result = text;
    if (result.back() != '\0') {
        result += '\0';
    }
    
    // 移除多余的null字符（保留最后一个）
    while (result.length() > 1 && result[result.length()-2] == '\0') {
        result.erase(result.length()-2, 1);
    }
    
    return result;
}

// ===============================================
// 原有的前向声明和全局变量
// ===============================================

// 前向声明
void ForceGarbageCollection();

// 定义Windows 10可能未包含的类型和常量
#ifndef MEMORY_PRIORITY_LOW
#define MEMORY_PRIORITY_LOW 2
#endif

#ifndef HeapCompatibilityInformation
#define HeapCompatibilityInformation 0
#endif

#ifndef ProcessMemoryPriority
#define ProcessMemoryPriority 0
#endif

#ifndef ProcessMemoryCompressionInformation
#define ProcessMemoryCompressionInformation 0
#endif

#ifndef ProcessMemoryAllocationTraceContext
#define ProcessMemoryAllocationTraceContext 0
#endif

typedef struct _PROCESS_MEMORY_COMPRESSION_INFORMATION {
    BOOLEAN Compressed;
} PROCESS_MEMORY_COMPRESSION_INFORMATION, *PPROCESS_MEMORY_COMPRESSION_INFORMATION;

typedef struct _MEMORY_ALLOCATION_TRACE_CONTEXT {
    ULONG ConfigurationId;
    ULONG Flags;
} MEMORY_ALLOCATION_TRACE_CONTEXT, *PMEMORY_ALLOCATION_TRACE_CONTEXT;

// 全局变量用于存储主进程ID和退出标志
DWORD g_MainProcessId = 0;
volatile BOOL g_ForceExit = FALSE;

// 全局变量，用于终止线程
volatile BOOL g_StopDelayThread = FALSE;
DWORD g_MainThreadId = 0;
HANDLE g_DelayThreadHandle = NULL;

// 全局变量用于存储上次调用的结果（向后兼容）
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0; // 0=int, 1=double, 2=string

// 线程函数参数结构
struct DelayThreadParams {
    int delayTime;
    volatile BOOL* pStopFlag;
    BOOL completed;
    DWORD startTick;
};

// 主进程退出检测线程函数
DWORD WINAPI MonitorProcessThread(LPVOID lpParam) {
    DWORD processId = *((DWORD*)lpParam);
    
    while (!g_ForceExit) {
        // 检查进程是否还在运行
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (hProcess == NULL) {
            // 进程已不存在，设置退出标志
            g_ForceExit = TRUE;
            break;
        }
        
        // 检查进程状态
        DWORD exitCode = 0;
        if (GetExitCodeProcess(hProcess, &exitCode) && exitCode != STILL_ACTIVE) {
            CloseHandle(hProcess);
            g_ForceExit = TRUE;
            break;
        }
        
        CloseHandle(hProcess);
        Sleep(100); // 每100ms检查一次
    }
    
                return 0;
}

// 初始化主进程监控
void InitProcessMonitor() {
    if (g_MainProcessId == 0) {
        g_MainProcessId = GetCurrentProcessId();
        g_ForceExit = FALSE;
        
        // 创建监控线程
        HANDLE hThread = CreateThread(NULL, 0, MonitorProcessThread, &g_MainProcessId, 0, NULL);
        if (hThread) {
            // 设置线程为低优先级
            SetThreadPriority(hThread, THREAD_PRIORITY_BELOW_NORMAL);
            // 设置线程为分离状态，避免资源泄漏
            CloseHandle(hThread);
        }
    }
}

// 检查是否收到退出请求
bool IsExitRequested() {
    return g_ForceExit == TRUE;
}

// 全局变量
std::set<std::string> g_excludedControls = { "按钮" };  // 默认排除按钮控件
std::string g_defaultConfigDir = "data";   // 默认配置文件目录

// 控件类型枚举
enum ControlType {
    CTRL_UNKNOWN = 0,
    CTRL_EDIT = 1,       // 编辑框
    CTRL_COMBOBOX = 2,   // 组合框
    CTRL_CHECKBOX = 3,   // 复选框
    CTRL_RADIOBUTTON = 4,// 单选框
    CTRL_LISTBOX = 5,    // 列表框
    CTRL_DATETIME = 6,   // 日期时间框
    CTRL_LISTVIEW = 7,   // 列表视图
    CTRL_TREEVIEW = 8    // 树视图
};

// 简单的字符串分割函数
std::vector<std::string> split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::string token;
    std::istringstream tokenStream(str);
    while (std::getline(tokenStream, token, delimiter)) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }
    return tokens;
}

// 简单的字符串修剪函数（去除首尾空格）
std::string trim(const std::string& str) {
    size_t first = str.find_first_not_of(" \t\n\r");
    if (first == std::string::npos) {
        return "";
    }
    size_t last = str.find_last_not_of(" \t\n\r");
    return str.substr(first, (last - first + 1));
}

// 确保目录存在
bool ensureDirectoryExists(const std::string& path) {
    size_t pos = 0;
    std::string dir;
    
    // 分割路径并逐级创建目录
    while ((pos = path.find_first_of("\\/", pos)) != std::string::npos) {
        dir = path.substr(0, pos++);
        if (!dir.empty() && dir.back() != ':') {  // 跳过驱动器前缀 (C:)
            if (_mkdir(dir.c_str()) != 0 && errno != EEXIST) {
                return false;  // 创建目录失败
            }
        }
    }
    return true;
}

// 获取默认配置文件路径
std::string getDefaultConfigPath() {
    char buffer[MAX_PATH];
    GetModuleFileNameA(NULL, buffer, MAX_PATH);
    
    std::string path(buffer);
    size_t lastSlash = path.find_last_of("\\/");
    if (lastSlash != std::string::npos) {
        path = path.substr(0, lastSlash + 1);
    }
    
    std::string configPath = path + g_defaultConfigDir;
    ensureDirectoryExists(configPath);  // 确保目录存在
    return configPath + "\\setsoft.ini";
}

// 获取控件类名
std::string getClassName(HWND hwnd) {
    char className[256] = {0};
                GetClassNameA(hwnd, className, sizeof(className));
    return std::string(className);
}

// 获取控件显示文本
std::string getWindowText(HWND hwnd) {
    int length = GetWindowTextLengthA(hwnd);
    if (length <= 0) {
        return "";
    }
    
    std::vector<char> buffer(length + 1);
    GetWindowTextA(hwnd, buffer.data(), length + 1);
    return std::string(buffer.data());
}

// 获取控件ID
int getControlId(HWND hwnd) {
    return GetDlgCtrlID(hwnd);
}

// 判断控件类型
ControlType getControlType(HWND hwnd) {
    std::string className = getClassName(hwnd);
    std::transform(className.begin(), className.end(), className.begin(), 
                  [](unsigned char c){ return std::tolower(c); });
    
    if (className == "edit") {
        return CTRL_EDIT;
    } else if (className == "combobox") {
        return CTRL_COMBOBOX;
    } else if (className == "button") {
        LONG style = GetWindowLongA(hwnd, GWL_STYLE);
        if ((style & BS_CHECKBOX) || (style & BS_AUTOCHECKBOX)) {
            return CTRL_CHECKBOX;
        } else if ((style & BS_RADIOBUTTON) || (style & BS_AUTORADIOBUTTON)) {
            return CTRL_RADIOBUTTON;
        }
    } else if (className == "listbox") {
        return CTRL_LISTBOX;
    } else if (className == "systreeview32") {
        return CTRL_TREEVIEW;
    } else if (className == "syslistview32") {
        return CTRL_LISTVIEW;
    } else if (className == "sysdatetimepick32") {
        return CTRL_DATETIME;
    }
    
    return CTRL_UNKNOWN;
}

// 获取控件的唯一名称（优先使用ID）
std::string getControlName(HWND hwnd) {
    int id = getControlId(hwnd);
    if (id > 0) {
        return "控件" + std::to_string(id);
    }
    
    // 尝试获取控件文本
    std::string text = getWindowText(hwnd);
    if (!text.empty()) {
        return text;
    }
    
    // 使用内存地址作为最后的备选
    std::stringstream ss;
    ss << "控件_" << hwnd;
    return ss.str();
}

// 获取控件的值
std::string getControlValue(HWND hwnd) {
    ControlType type = getControlType(hwnd);
    std::string value;
    
    switch (type) {
        case CTRL_EDIT: {
            value = getWindowText(hwnd);
            break;
        }
        case CTRL_COMBOBOX: {
            int index = SendMessageA(hwnd, CB_GETCURSEL, 0, 0);
            if (index != CB_ERR) {
                int textLen = SendMessageA(hwnd, CB_GETLBTEXTLEN, index, 0);
                if (textLen > 0) {
                    std::vector<char> buffer(textLen + 1);
                    SendMessageA(hwnd, CB_GETLBTEXT, index, (LPARAM)buffer.data());
                    value = std::string(buffer.data());
                } else {
                    value = getWindowText(hwnd);
            }
        } else {
                value = getWindowText(hwnd);
            }
            break;
        }
        case CTRL_CHECKBOX:
        case CTRL_RADIOBUTTON: {
            LRESULT state = SendMessageA(hwnd, BM_GETCHECK, 0, 0);
            value = (state == BST_CHECKED) ? "1" : "0";
            break;
        }
        case CTRL_LISTBOX: {
            int count = SendMessageA(hwnd, LB_GETCOUNT, 0, 0);
            std::vector<std::string> selected;
            
            for (int i = 0; i < count; i++) {
                if (SendMessageA(hwnd, LB_GETSEL, i, 0) > 0) {
                    int textLen = SendMessageA(hwnd, LB_GETTEXTLEN, i, 0);
                    if (textLen > 0) {
                        std::vector<char> buffer(textLen + 1);
                        SendMessageA(hwnd, LB_GETTEXT, i, (LPARAM)buffer.data());
                        selected.push_back(std::string(buffer.data()));
                    }
                }
            }
            
            if (!selected.empty()) {
                for (size_t i = 0; i < selected.size(); i++) {
                    if (i > 0) value += ",";
                    value += selected[i];
                }
            }
            break;
        }
        case CTRL_DATETIME: {
    SYSTEMTIME st;
            if (SendMessageA(hwnd, DTM_GETSYSTEMTIME, 0, (LPARAM)&st)) {
                char buffer[128];
                sprintf_s(buffer, "%04d-%02d-%02d %02d:%02d:%02d", 
        st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                value = buffer;
            }
            break;
        }
        default:
            value = getWindowText(hwnd);
            break;
    }
    
    return value;
}

// 设置控件的值
bool setControlValue(HWND hwnd, const std::string& value) {
    ControlType type = getControlType(hwnd);
    
    switch (type) {
        case CTRL_EDIT: {
            SetWindowTextA(hwnd, value.c_str());
            return true;
        }
        case CTRL_COMBOBOX: {
            // 尝试查找匹配项
            int count = SendMessageA(hwnd, CB_GETCOUNT, 0, 0);
            for (int i = 0; i < count; i++) {
                int textLen = SendMessageA(hwnd, CB_GETLBTEXTLEN, i, 0);
                if (textLen > 0) {
                    std::vector<char> buffer(textLen + 1);
                    SendMessageA(hwnd, CB_GETLBTEXT, i, (LPARAM)buffer.data());
                    if (value == std::string(buffer.data())) {
                        SendMessageA(hwnd, CB_SETCURSEL, i, 0);
                        return true;
                    }
                }
            }
            
            // 如果没找到匹配项但是组合框是可编辑的，直接设置文本
            SetWindowTextA(hwnd, value.c_str());
            return true;
        }
        case CTRL_CHECKBOX:
        case CTRL_RADIOBUTTON: {
            bool checked = (value == "1" || value == "true" || value == "yes");
            SendMessageA(hwnd, BM_SETCHECK, checked ? BST_CHECKED : BST_UNCHECKED, 0);
            return true;
        }
        case CTRL_LISTBOX: {
            // 清除所有选择
            SendMessageA(hwnd, LB_SETSEL, FALSE, -1);
            
            // 分割多选值
            std::vector<std::string> items = split(value, ',');
            int count = SendMessageA(hwnd, LB_GETCOUNT, 0, 0);
            
            for (const auto& item : items) {
                std::string trimmedItem = trim(item);
                for (int i = 0; i < count; i++) {
                    int textLen = SendMessageA(hwnd, LB_GETTEXTLEN, i, 0);
                    if (textLen > 0) {
                        std::vector<char> buffer(textLen + 1);
                        SendMessageA(hwnd, LB_GETTEXT, i, (LPARAM)buffer.data());
                        if (trimmedItem == std::string(buffer.data())) {
                            SendMessageA(hwnd, LB_SETSEL, TRUE, i);
                            break;
                        }
                    }
                }
            }
            return true;
        }
        case CTRL_DATETIME: {
            // 尝试解析日期时间格式 YYYY-MM-DD HH:MM:SS
            SYSTEMTIME st = {0};
            if (sscanf_s(value.c_str(), "%hd-%hd-%hd %hd:%hd:%hd", 
                    &st.wYear, &st.wMonth, &st.wDay, &st.wHour, &st.wMinute, &st.wSecond) >= 3) {
                SendMessageA(hwnd, DTM_SETSYSTEMTIME, 0, (LPARAM)&st);
                return true;
            }
            return false;
        }
        default:
            // 对于不支持的控件类型，尝试设置文本
            SetWindowTextA(hwnd, value.c_str());
            return true;
    }
}

// 保存配置到文件
bool saveConfigToFile(const std::string& filePath, const std::map<std::string, std::string>& data) {
    try {
        // 确保目录存在
        std::string path = filePath;
        size_t lastSlash = path.find_last_of("\\/");
        if (lastSlash != std::string::npos) {
            ensureDirectoryExists(path.substr(0, lastSlash));
        }
        
        std::ofstream file(filePath.c_str());
        if (!file.is_open()) {
            return false;
        }
        
        for (const auto& pair : data) {
            file << pair.first << "=" << pair.second << std::endl;
        }
        
        file.close();
        return true;
    }
    catch (...) {
        return false;
    }
}

// 从文件加载配置
bool loadConfigFromFile(const std::string& filePath, std::map<std::string, std::string>& data) {
    try {
        std::ifstream file(filePath.c_str());
        if (!file.is_open()) {
            return false;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            line = trim(line);
            if (line.empty() || line[0] == '#' || line[0] == ';') {
                continue;  // 跳过空行和注释
            }
            
            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = trim(line.substr(0, pos));
                std::string value = trim(line.substr(pos + 1));
                data[key] = value;
            }
        }
        
        file.close();
        return true;
    }
    catch (...) {
        return false;
    }
}

// 递归枚举窗口中的所有控件
BOOL CALLBACK EnumChildProc(HWND hwnd, LPARAM lParam) {
    auto* controlsMap = reinterpret_cast<std::map<std::string, HWND>*>(lParam);
    
    // 获取控件名称
    std::string name = getControlName(hwnd);
    std::string className = getClassName(hwnd);
    
    // 检查是否应该排除此控件
    bool shouldExclude = false;
    
    // 排除所有按钮控件
    if (className == "Button") {
        // 检查是否为普通按钮（非复选框或单选框）
        LONG style = GetWindowLongA(hwnd, GWL_STYLE);
        if (!((style & BS_CHECKBOX) || (style & BS_AUTOCHECKBOX) || 
              (style & BS_RADIOBUTTON) || (style & BS_AUTORADIOBUTTON))) {
            shouldExclude = true;
        }
    }
    
    // 检查控件名称是否包含关键字（例如 "按钮"）
    for (const auto& keyword : g_excludedControls) {
        if (name.find(keyword) != std::string::npos) {
            shouldExclude = true;
            break;
        }
    }
    
    if (!shouldExclude) {
        (*controlsMap)[name] = hwnd;
    }
    
    return TRUE;  // 继续枚举
}

/**
 * 简化的保存配置函数 - 只需要配置文件路径和父窗口句柄
 */
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    try {
        // 处理配置文件路径
        std::string filePath;
        if (configFile && configFile[0] != '\0') {
            filePath = configFile;
        } else {
            filePath = getDefaultConfigPath();
        }
        
        // 获取窗口句柄
        HWND parentHwnd = (HWND)parentWindow;
        if (!parentHwnd) {
            parentHwnd = GetActiveWindow();
        }
        
        if (!parentHwnd) {
            return 0;  // 无法获取有效窗口
        }
        
        // 加载现有配置
        std::map<std::string, std::string> configData;
        loadConfigFromFile(filePath, configData);  // 即使加载失败也继续
        
        // 枚举窗口中的所有控件
        std::map<std::string, HWND> controls;
        EnumChildWindows(parentHwnd, EnumChildProc, (LPARAM)&controls);
        
        // 保存所有控件值到配置
        for (const auto& pair : controls) {
            const std::string& name = pair.first;
            HWND hwnd = pair.second;
            
            std::string value = getControlValue(hwnd);
            configData[name] = value;
        }
        
        // 保存到文件
        if (saveConfigToFile(filePath, configData)) {
            return static_cast<int>(controls.size());  // 返回保存的控件数量
        }
        
        return 0;  // 保存失败
    }
    catch (...) {
        return 0;
    }
}

/**
 * 简化的读取配置函数 - 只需要配置文件路径和父窗口句柄
 */
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    try {
        // 处理配置文件路径
        std::string filePath;
        if (configFile && configFile[0] != '\0') {
            filePath = configFile;
        } else {
            filePath = getDefaultConfigPath();
        }
        
        // 获取窗口句柄
        HWND parentHwnd = (HWND)parentWindow;
        if (!parentHwnd) {
            parentHwnd = GetActiveWindow();
        }
        
        if (!parentHwnd) {
            return 0;  // 无法获取有效窗口
        }
        
        // 加载配置数据
        std::map<std::string, std::string> configData;
        if (!loadConfigFromFile(filePath, configData)) {
            return 0;  // 读取配置文件失败
        }
        
        // 枚举窗口中的所有控件
        std::map<std::string, HWND> controls;
        EnumChildWindows(parentHwnd, EnumChildProc, (LPARAM)&controls);
        
        // 设置控件值
        int count = 0;
        for (const auto& pair : controls) {
            const std::string& name = pair.first;
            HWND hwnd = pair.second;
            
            auto it = configData.find(name);
            if (it != configData.end()) {
                if (setControlValue(hwnd, it->second)) {
                    count++;
                }
            }
        }
        
        return count;  // 返回成功设置的控件数量
    }
    catch (...) {
        return 0;
    }
}

// 检查是否为易语言窗口的辅助函数
bool IsELanguageWindow(HWND hwnd) {
    char className[256] = {0};
    GetClassNameA(hwnd, className, sizeof(className));
    
    // 易语言主窗口类名特征
    if (strstr(className, "EFormDesign") != NULL ||
        strstr(className, "EForm") != NULL ||
        strstr(className, "ElangWindow") != NULL) {
        return true;
    }
    
    // 检查窗口标题
    char title[256] = {0};
    GetWindowTextA(hwnd, title, sizeof(title));
    if (strstr(title, "易语言") != NULL || 
        strstr(title, "E语言") != NULL) {
        return true;
    }
    
    return false;
}

// 查找当前进程的所有易语言窗口
void FindELanguageWindows(HWND* windows, int& count, int maxCount) {
    count = 0;
    DWORD currentPid = GetCurrentProcessId();
    
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        if (!IsWindowVisible(hwnd)) return TRUE;
        
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        
        auto params = (std::pair<HWND*, std::pair<int&, int>>*)lParam;
        HWND* windows = params->first;
        int& count = params->second.first;
        int maxCount = params->second.second;
        
        if (pid == GetCurrentProcessId()) {
            // 尝试识别是否为易语言窗口
            if (IsELanguageWindow(hwnd) || (GetWindowLongA(hwnd, GWL_STYLE) & WS_CAPTION)) {
                if (count < maxCount) {
                    windows[count++] = hwnd;
                }
            }
        }
            return TRUE;
    }, (LPARAM)&std::make_pair(windows, std::make_pair(std::ref(count), maxCount)));
}

// 窗口钩子过程
LRESULT CALLBACK WindowProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0) {
        if (wParam == WM_DESTROY || wParam == WM_CLOSE || wParam == WM_QUIT) {
            // 收到窗口关闭消息，设置停止标志
            g_StopDelayThread = TRUE;
            
            // 如果线程句柄有效，等待其结束
            if (g_DelayThreadHandle) {
                // 给线程200ms时间自行退出
                if (WaitForSingleObject(g_DelayThreadHandle, 200) == WAIT_TIMEOUT) {
                    // 如果线程没有及时退出，强制终止它
                    TerminateThread(g_DelayThreadHandle, 0);
                }
                CloseHandle(g_DelayThreadHandle);
                g_DelayThreadHandle = NULL;
            }
        }
    }
    
    return CallNextHookEx(NULL, nCode, wParam, lParam);
}

// 延时线程函数
unsigned __stdcall DelayThreadProc(void* pvParam) {
    DelayThreadParams* pParams = (DelayThreadParams*)pvParam;
    DWORD endTime = pParams->startTick + pParams->delayTime;
    
    // 保存当前窗口信息
    HWND mainWindow = GetForegroundWindow();
    HWND lastActiveWindow = NULL;
    DWORD lastCheckTime = GetTickCount();
    
    // 寻找并记录进程中的所有顶级窗口
    std::vector<HWND> processWindows;
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        if (!IsWindowVisible(hwnd)) return TRUE;
        
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        if (pid == GetCurrentProcessId()) {
            std::vector<HWND>* pWindows = (std::vector<HWND>*)lParam;
            pWindows->push_back(hwnd);
        }
        return TRUE;
    }, (LPARAM)&processWindows);
    
    while (GetTickCount() < endTime) {
        // 检查停止标志
        if (*(pParams->pStopFlag)) {
            break;
        }
        
        // 处理窗口消息
        MSG msg;
        while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
            if (msg.message == WM_QUIT || msg.message == WM_CLOSE || msg.message == WM_DESTROY) {
                *(pParams->pStopFlag) = TRUE;
                break;
            }
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        // 定期检查窗口状态
        DWORD currentTime = GetTickCount();
        if (currentTime - lastCheckTime > 50) {
            lastCheckTime = currentTime;
            
            // 检查之前记录的所有窗口是否都已关闭
            bool allWindowsClosed = true;
            for (HWND hwnd : processWindows) {
                if (IsWindow(hwnd) && IsWindowVisible(hwnd)) {
                    allWindowsClosed = false;
                    break;
                }
            }
            
            if (allWindowsClosed && !processWindows.empty()) {
                // 所有窗口都已关闭，终止线程
                *(pParams->pStopFlag) = TRUE;
                break;
            }
            
            // 检查主窗口状态
            if (mainWindow && !IsWindow(mainWindow)) {
                // 主窗口已关闭
                *(pParams->pStopFlag) = TRUE;
                break;
            }
            
            // 获取当前活动窗口
            HWND activeWindow = GetForegroundWindow();
            if (activeWindow != lastActiveWindow) {
                lastActiveWindow = activeWindow;
                
                // 检查活动窗口是否变化到其他程序
                DWORD activePid = 0;
                if (activeWindow) {
                    GetWindowThreadProcessId(activeWindow, &activePid);
                    
                    // 如果活动窗口变化到其他程序，并且我们的主窗口已经不可见
                    if (activePid != GetCurrentProcessId() && mainWindow && 
                        (!IsWindow(mainWindow) || !IsWindowVisible(mainWindow))) {
                        // 再次检查是否有任何可见窗口
                        bool hasVisibleWindow = false;
                        EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
                            DWORD pid = 0;
                            GetWindowThreadProcessId(hwnd, &pid);
                            if (pid == GetCurrentProcessId() && IsWindowVisible(hwnd)) {
                                *(bool*)lParam = true;
                                return FALSE;
                            }
                            return TRUE;
                        }, (LPARAM)&hasVisibleWindow);
                        
                        if (!hasVisibleWindow) {
                            *(pParams->pStopFlag) = TRUE;
                break;
                        }
                    }
                }
            }
        }
        
        // 内存优化
        static int loopCount = 0;
        if (++loopCount % 500 == 0) {
            SetProcessWorkingSetSize(GetCurrentProcess(), (SIZE_T)-1, (SIZE_T)-1);
        }
        
        Sleep(1); // 短暂休眠，减轻CPU负担
    }
    
    // 标记延时完成
    pParams->completed = TRUE;
                        return 0;
                    }

/**
 * 程序延时函数 - 终极优化版
 * 
 * @param delayTime 延时毫秒数
 * @return 成功返回1，失败返回0
 */
MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

// 辅助函数 - 强制垃圾回收
void ForceGarbageCollection() {
    // 清理文件缓存
    FlushFileBuffers(NULL);
    
    // 尝试回收更多内存资源
    // 这里使用一些技巧强制系统回收内存
    for (int i = 0; i < 3; i++) {
        void* p = VirtualAlloc(NULL, 256 * 1024 * 1024, MEM_RESERVE, PAGE_NOACCESS);
        if (p) {
            VirtualFree(p, 0, MEM_RELEASE);
        }
    }
    
    // 请求系统进行全面内存回收
    SetProcessWorkingSetSize(GetCurrentProcess(), (SIZE_T)-1, (SIZE_T)-1);
    EmptyWorkingSet(GetCurrentProcess());
}

// 核心的RemoteCallDLL函数 - 支持字符串返回
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName, const char* parameters, int paramCount, void* result, int resultType, int bufferSize)
{
    if (!remotePath || !functionName || !result) {
                return 0;
            }

    // 下载DLL到临时文件
    char tempDir[MAX_PATH];
    char dllPath[MAX_PATH];
    GetTempPathA(MAX_PATH, tempDir);
    sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());
    
    BOOL needCleanup = FALSE;
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            // 下载失败，设置错误结果
            if (resultType == 3 || resultType == 5) {
                strcpy_s((char*)result, bufferSize, "下载失败");
    } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "下载失败");
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            g_LastCallResultType = 2;
        return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }
    
    // 加载DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "加载失败");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "加载失败");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        g_LastCallResultType = 2;
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // 获取函数地址
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        // 函数不存在
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "函数不存在");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "函数不存在");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        g_LastCallResultType = 2;
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // 调用函数
    int functionResult = 0;
    char rawStringResult[2048] = {0};  // 增大缓冲区
    bool hasStringResult = false;
    bool callSuccess = false;
    
    try {
        // 🎯 增强的字符串函数检测
        bool isStringFunction = false;
        
        // 检查函数名是否包含文本相关关键字
        if (strstr(functionName, "文本") != NULL ||
            strstr(functionName, "字符串") != NULL ||
            strstr(functionName, "测试") != NULL ||
            strstr(functionName, "String") != NULL ||
            strstr(functionName, "Text") != NULL ||
            strstr(functionName, "Get") != NULL ||
            strstr(functionName, "Info") != NULL ||
            strstr(functionName, "Version") != NULL) {
            isStringFunction = true;
        }
        
        if (isStringFunction) {
            // 🎯 字符串返回函数处理 - 使用编码优化
            switch (paramCount) {
        case 0:
            {
                        typedef const char* (__stdcall *StringFunc0)();
                        StringFunc0 func = (StringFunc0)procAddr;
                        const char* strResult = func();
                        
                        if (strResult && strlen(strResult) > 0) {
                            // 🔥 使用新的智能字符串处理
                            std::string processedString = SmartStringProcess(strResult, strlen(strResult));
                            
                            if (!processedString.empty() && processedString != "空数据" && processedString != "无效文本数据") {
                                strncpy_s(rawStringResult, sizeof(rawStringResult), processedString.c_str(), _TRUNCATE);
                                hasStringResult = true;
                                callSuccess = true;
                                functionResult = 1;
                            } else {
                                // 🔍 编码检测和转换
                                EncodingInfo encoding = DetectEncoding(strResult, strlen(strResult));
                                
                                char debugInfo[512];
                                sprintf_s(debugInfo, sizeof(debugInfo), 
                                    "编码类型:%d,置信度:%d,原始长度:%d", 
                                    encoding.type, encoding.confidence, (int)strlen(strResult));
                                
                                // 尝试多种编码转换
                                std::string converted;
                                if (encoding.type == ENCODING_UTF8) {
                                    converted = ConvertEncoding(strResult, strlen(strResult), ENCODING_UTF8, ENCODING_ANSI);
                                } else if (encoding.type == ENCODING_UTF16LE) {
                                    converted = ConvertEncoding(strResult, strlen(strResult), ENCODING_UTF16LE, ENCODING_ANSI);
                                } else {
                                    converted = std::string(strResult);
                                }
                                
                                if (!converted.empty()) {
                                    strncpy_s(rawStringResult, sizeof(rawStringResult), converted.c_str(), _TRUNCATE);
                                    hasStringResult = true;
                                    callSuccess = true;
                                    functionResult = 1;
                                } else {
                                    sprintf_s(rawStringResult, sizeof(rawStringResult), "编码转换失败[%s]", debugInfo);
                                    hasStringResult = true;
                                    callSuccess = true;
                                    functionResult = 0;
                                }
                            }
                        } else {
                            strcpy_s(rawStringResult, sizeof(rawStringResult), "函数返回空值");
                            hasStringResult = true;
                            callSuccess = true;
                            functionResult = 0;
                        }
            }
            break;
                    
        case 1:
            {
                        typedef const char* (__stdcall *StringFunc1)(int);
                        StringFunc1 func = (StringFunc1)procAddr;
                        int param = parameters ? atoi(parameters) : 0;
                        const char* strResult = func(param);
                        
                        if (strResult && strlen(strResult) > 0) {
                            std::string processedString = SmartStringProcess(strResult, strlen(strResult));
                            strncpy_s(rawStringResult, sizeof(rawStringResult), processedString.c_str(), _TRUNCATE);
                            hasStringResult = true;
                            callSuccess = true;
                            functionResult = 1;
                        } else {
                            strcpy_s(rawStringResult, sizeof(rawStringResult), "函数返回空值");
                            hasStringResult = true;
                            callSuccess = true;
                            functionResult = 0;
                        }
            }
            break;
                    
                default:
                    strcpy_s(rawStringResult, sizeof(rawStringResult), "参数个数不支持");
                    hasStringResult = true;
                    callSuccess = true;
                    functionResult = 0;
            break;
            }
        } else {
            // 🔢 数值返回函数处理（保持原有逻辑）
            switch (paramCount) {
                case 0:
                    {
                        typedef int (__stdcall *Func0)();
                        Func0 func = (Func0)procAddr;
                        functionResult = func();
                        callSuccess = true;
            }
            break;
                case 1:
                    {
                        typedef int (__stdcall *Func1)(int);
                        Func1 func = (Func1)procAddr;
                        int param = parameters ? atoi(parameters) : 0;
                        functionResult = func(param);
                        callSuccess = true;
            }
            break;
                case 2:
                    {
                        // 解析两个参数
                        int param1 = 0, param2 = 0;
                        if (parameters) {
                            char paramsCopy[256];
                            strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);
                            char* comma = strchr(paramsCopy, ',');
                            if (comma) {
                                *comma = '\0';
                                param1 = atoi(paramsCopy);
                                param2 = atoi(comma + 1);
                            } else {
                                param1 = atoi(paramsCopy);
                            }
                        }
                        
                        typedef int (__stdcall *Func2)(int, int);
                        Func2 func = (Func2)procAddr;
                        functionResult = func(param1, param2);
                        callSuccess = true;
            }
            break;
        default:
                    functionResult = -1;
                    callSuccess = false;
            break;
            }
        }
        
        // 🎯 设置结果 - 使用编码处理
        if (callSuccess) {
            if (hasStringResult) {
                // 字符串结果 - 应用最终的易语言格式化
                std::string finalString = FormatForELanguage(std::string(rawStringResult));
                
            switch (resultType) {
                case 1: // 整数结果
                    *(int*)result = functionResult;
                    break;
                case 2: // 浮点数结果
                    *(double*)result = (double)functionResult;
                    break;
                case 3: // 字符串结果
                        {
                            // 确保不会缓冲区溢出
                            int copyLen = min((int)finalString.length(), bufferSize - 1);
                            memcpy((char*)result, finalString.c_str(), copyLen);
                            ((char*)result)[copyLen] = '\0';
                        }
                    break;
                case 4: // 逻辑型结果
                    *(BOOL*)result = (functionResult != 0);
                    break;
                    case 5: // 自动模式（优先显示字符串）
                        {
                            int copyLen = min((int)finalString.length(), bufferSize - 1);
                            memcpy((char*)result, finalString.c_str(), copyLen);
                            ((char*)result)[copyLen] = '\0';
                    }
                    break;
                case 6: // 长整数结果
                    *(long long*)result = (long long)functionResult;
                    break;
        }
        
                // 存储到全局变量
                strncpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), finalString.c_str(), _TRUNCATE);
            g_LastCallResult = functionResult;
                g_LastCallResultType = 2; // 字符串类型
            } else {
                // 数值结果（保持原有逻辑）
            switch (resultType) {
                case 1: // 整数结果
                        *(int*)result = functionResult;
                    break;
                case 2: // 浮点数结果
                        *(double*)result = (double)functionResult;
                    break;
                case 3: // 字符串结果
                        sprintf_s((char*)result, bufferSize, "%d", functionResult);
                    break;
                case 4: // 逻辑型结果
                        *(BOOL*)result = (functionResult != 0);
                    break;
                case 5: // 自动模式
                        sprintf_s((char*)result, bufferSize, "%d", functionResult);
                    break;
                case 6: // 长整数结果
                        *(long long*)result = (long long)functionResult;
                    break;
            }
                
                // 存储到全局变量
                sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", functionResult);
                g_LastCallResult = functionResult;
                g_LastCallResultType = 0; // 整数类型
            }
            
            g_LastCallResultDouble = (double)functionResult;
            g_LastCallHasResult = TRUE;
        } else {
            // 调用失败
            if (resultType == 3 || resultType == 5) {
                strcpy_s((char*)result, bufferSize, "调用失败");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "调用失败");
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            g_LastCallResultType = 2;
        }
        
    } catch (...) {
        // 异常处理
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "调用异常");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "调用异常");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        g_LastCallResultType = 2;
    }
    
    // 清理资源
    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);
    
    return 1;
}

// 向后兼容函数
MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;
    
    int len = strlen(g_LastCallResultString);
    int copyLen = min(len, bufferSize - 1);
    
    if (copyLen > 0) {
        memcpy(buffer, g_LastCallResultString, copyLen);
    }
    buffer[copyLen] = '\0';
    
    return copyLen;
}

MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

// DLL入口函数
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // 设置中文支持
        setlocale(LC_ALL, "C");
        
        // 初始化
        g_MainProcessId = GetCurrentProcessId();
        g_MainThreadId = GetCurrentThreadId();
        g_ForceExit = FALSE;
        g_MainWindow = NULL;
        
        // 初始化全局变量
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        break;
        
    case DLL_PROCESS_DETACH:
        g_ForceExit = TRUE;
        break;
    }
    
    return TRUE;
} 
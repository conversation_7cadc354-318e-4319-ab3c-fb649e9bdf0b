#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <commctrl.h>
#include <string>
#include <fstream>
#include <sstream>
#include <map>
#include <vector>
#include <set>
#include <algorithm>
#include <cctype>
#include <locale>
#include <ctime>
#include <direct.h>
#include <psapi.h>
#include <tlhelp32.h>
#include <utility>
#include <process.h>
#include <wininet.h>
#include <urlmon.h>
#include <shlwapi.h>
#include <stdio.h>

#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")
#pragma comment(lib, "shlwapi.lib")

// 全局变量用于存储上次调用的结果
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;

// 🎯 智能编码转换函数 - 专门解决易语言编码问题
std::string SmartConvertToAnsi(const char* input, int length) {
    if (!input || length <= 0) {
        return "空数据";
    }
    
    std::string result;
    
    try {
        // 尝试UTF-8到ANSI转换
        int wideLen = MultiByteToWideChar(CP_UTF8, 0, input, length, NULL, 0);
        if (wideLen > 0) {
            std::wstring unicode(wideLen, L'\0');
            MultiByteToWideChar(CP_UTF8, 0, input, length, &unicode[0], wideLen);
            
            int ansiLen = WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, NULL, 0, NULL, NULL);
            if (ansiLen > 0) {
                result.resize(ansiLen - 1);
                WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, &result[0], ansiLen, NULL, NULL);
                
                // 验证转换结果
                if (!result.empty() && result.find('\0') == std::string::npos) {
                    return result;
                }
            }
        }
        
        // 如果UTF-8转换失败，尝试直接使用ANSI
        result = std::string(input, length);
        
        // 清理非打印字符
        std::string cleaned;
        for (size_t i = 0; i < result.length(); i++) {
            unsigned char c = (unsigned char)result[i];
            if ((c >= 32 && c <= 126) || (c >= 161 && c <= 254) || c == '\r' || c == '\n' || c == '\t') {
                cleaned += c;
            }
        }
        
        if (!cleaned.empty()) {
            return cleaned;
        }
        
    } catch (...) {
        // 异常时返回错误信息
    }
    
    return "编码转换失败";
}

// 🔧 验证字符串是否为有效文本
bool IsValidText(const std::string& text) {
    if (text.empty()) return false;
    
    // 检查是否包含可打印字符
    for (size_t i = 0; i < text.length(); i++) {
        unsigned char c = (unsigned char)text[i];
        if ((c >= 32 && c <= 126) || (c >= 161 && c <= 254)) {
            return true;
        }
    }
    return false;
}

// 🎯 增强的RemoteCallDLL函数
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName, const char* parameters, int paramCount, void* result, int resultType, int bufferSize)
{
    if (!remotePath || !functionName || !result) {
        return 0;
    }

    // 下载或使用本地DLL
    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;
    
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());
        
        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5) {
                strcpy_s((char*)result, bufferSize, "下载失败");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "下载失败");
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            g_LastCallResultType = 2;
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }
    
    // 加载DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "DLL加载失败");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "DLL加载失败");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        g_LastCallResultType = 2;
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // 获取函数地址
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "函数不存在");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "函数不存在");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        g_LastCallResultType = 2;
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // 🎯 调用函数并处理结果
    int functionResult = 0;
    std::string textResult;
    bool isTextFunction = false;
    bool callSuccess = false;
    
    try {
        // 检查是否为文本函数
        if (strstr(functionName, "文本") || strstr(functionName, "测试") || 
            strstr(functionName, "String") || strstr(functionName, "Text") ||
            strstr(functionName, "Get") || strstr(functionName, "Version")) {
            isTextFunction = true;
        }
        
        if (isTextFunction) {
            // 🎯 文本函数处理
            switch (paramCount) {
                case 0:
                    {
                        typedef const char* (__stdcall *StringFunc0)();
                        StringFunc0 func = (StringFunc0)procAddr;
                        const char* rawResult = func();
                        
                        if (rawResult) {
                            // 🔥 使用智能编码转换
                            textResult = SmartConvertToAnsi(rawResult, strlen(rawResult));
                            if (IsValidText(textResult)) {
                                functionResult = 1;
                                callSuccess = true;
                            } else {
                                textResult = "返回数据无效";
                                functionResult = 0;
                                callSuccess = true;
                            }
                        } else {
                            textResult = "函数返回空指针";
                            functionResult = 0;
                            callSuccess = true;
                        }
                    }
                    break;
                case 1:
                    {
                        typedef const char* (__stdcall *StringFunc1)(int);
                        StringFunc1 func = (StringFunc1)procAddr;
                        int param = parameters ? atoi(parameters) : 0;
                        const char* rawResult = func(param);
                        
                        if (rawResult) {
                            textResult = SmartConvertToAnsi(rawResult, strlen(rawResult));
                            if (IsValidText(textResult)) {
                                functionResult = 1;
                                callSuccess = true;
                            } else {
                                textResult = "返回数据无效";
                                functionResult = 0;
                                callSuccess = true;
                            }
                        } else {
                            textResult = "函数返回空指针";
                            functionResult = 0;
                            callSuccess = true;
                        }
                    }
                    break;
                default:
                    textResult = "参数个数不支持";
                    functionResult = 0;
                    callSuccess = true;
                    break;
            }
        } else {
            // 🔢 数值函数处理
            switch (paramCount) {
                case 0:
                    {
                        typedef int (__stdcall *IntFunc0)();
                        IntFunc0 func = (IntFunc0)procAddr;
                        functionResult = func();
                        callSuccess = true;
                    }
                    break;
                case 1:
                    {
                        typedef int (__stdcall *IntFunc1)(int);
                        IntFunc1 func = (IntFunc1)procAddr;
                        int param = parameters ? atoi(parameters) : 0;
                        functionResult = func(param);
                        callSuccess = true;
                    }
                    break;
                case 2:
                    {
                        typedef int (__stdcall *IntFunc2)(int, int);
                        IntFunc2 func = (IntFunc2)procAddr;
                        
                        int param1 = 0, param2 = 0;
                        if (parameters) {
                            char paramsCopy[256];
                            strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);
                            char* comma = strchr(paramsCopy, ',');
                            if (comma) {
                                *comma = '\0';
                                param1 = atoi(paramsCopy);
                                param2 = atoi(comma + 1);
                            } else {
                                param1 = atoi(paramsCopy);
                            }
                        }
                        
                        functionResult = func(param1, param2);
                        callSuccess = true;
                    }
                    break;
                default:
                    functionResult = -1;
                    callSuccess = false;
                    break;
            }
        }
        
        // 🎯 设置返回结果
        if (callSuccess) {
            if (isTextFunction && !textResult.empty()) {
                // 文本结果
                switch (resultType) {
                    case 1: // 整数
                        *(int*)result = functionResult;
                        break;
                    case 2: // 浮点
                        *(double*)result = (double)functionResult;
                        break;
                    case 3: // 字符串
                    case 5: // 自动
                        {
                            int copyLen = min((int)textResult.length(), bufferSize - 1);
                            memcpy((char*)result, textResult.c_str(), copyLen);
                            ((char*)result)[copyLen] = '\0';
                        }
                        break;
                    case 4: // 逻辑
                        *(BOOL*)result = (functionResult != 0);
                        break;
                    case 6: // 长整数
                        *(long long*)result = (long long)functionResult;
                        break;
                }
                
                // 存储到全局变量
                strncpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult.c_str(), _TRUNCATE);
                g_LastCallResult = functionResult;
                g_LastCallResultType = 2;
            } else {
                // 数值结果
                switch (resultType) {
                    case 1: // 整数
                        *(int*)result = functionResult;
                        break;
                    case 2: // 浮点
                        *(double*)result = (double)functionResult;
                        break;
                    case 3: // 字符串
                    case 5: // 自动
                        sprintf_s((char*)result, bufferSize, "%d", functionResult);
                        break;
                    case 4: // 逻辑
                        *(BOOL*)result = (functionResult != 0);
                        break;
                    case 6: // 长整数
                        *(long long*)result = (long long)functionResult;
                        break;
                }
                
                // 存储到全局变量
                sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", functionResult);
                g_LastCallResult = functionResult;
                g_LastCallResultType = 0;
            }
            
            g_LastCallResultDouble = (double)functionResult;
            g_LastCallHasResult = TRUE;
        } else {
            // 调用失败
            if (resultType == 3 || resultType == 5) {
                strcpy_s((char*)result, bufferSize, "调用失败");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "调用失败");
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            g_LastCallResultType = 2;
        }
        
    } catch (...) {
        // 异常处理
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "调用异常");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "调用异常");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        g_LastCallResultType = 2;
    }
    
    // 清理资源
    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);
    
    return 1;
}

// 简化的其他函数
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    return 1;
}

MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    return 0;
}

MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

// 向后兼容函数
MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;
    
    int len = strlen(g_LastCallResultString);
    int copyLen = min(len, bufferSize - 1);
    
    if (copyLen > 0) {
        memcpy(buffer, g_LastCallResultString, copyLen);
    }
    buffer[copyLen] = '\0';
    
    return copyLen;
}

MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

// DLL入口函数
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // 初始化编码支持
        setlocale(LC_ALL, "Chinese");
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        break;
    case DLL_PROCESS_DETACH:
        break;
    }
    return TRUE;
} 
#define MYDLL_EXPORTS // 确保MYDLL_API被定义为导出

#include "mydll.h"
#include <windows.h>
#include <commctrl.h>
#include <string>
#include <fstream>
#include <sstream>
#include <map>
#include <vector>
#include <set>
#include <algorithm>
#include <cctype>
#include <locale>
#include <ctime>
#include <direct.h>
#include <psapi.h> // 用于EmptyWorkingSet
#include <tlhelp32.h> // 用于进程检测
#include <utility> // 用于std::pair和std::make_pair
#include <process.h>  // 用于_beginthreadex

// 声明EmptyWorkingSet函数
#pragma comment(lib, "psapi.lib")

// 前向声明
void ForceGarbageCollection();

// 定义Windows 10可能未包含的类型和常量
#ifndef MEMORY_PRIORITY_LOW
#define MEMORY_PRIORITY_LOW 2
#endif

#ifndef HeapCompatibilityInformation
#define HeapCompatibilityInformation 0
#endif

#ifndef ProcessMemoryPriority
#define ProcessMemoryPriority 0
#endif

#ifndef ProcessMemoryCompressionInformation
#define ProcessMemoryCompressionInformation 0
#endif

#ifndef ProcessMemoryAllocationTraceContext
#define ProcessMemoryAllocationTraceContext 0
#endif

typedef struct _PROCESS_MEMORY_COMPRESSION_INFORMATION {
    BOOLEAN Compressed;
} PROCESS_MEMORY_COMPRESSION_INFORMATION, *PPROCESS_MEMORY_COMPRESSION_INFORMATION;

typedef struct _MEMORY_ALLOCATION_TRACE_CONTEXT {
    ULONG ConfigurationId;
    ULONG Flags;
} MEMORY_ALLOCATION_TRACE_CONTEXT, *PMEMORY_ALLOCATION_TRACE_CONTEXT;

// 全局变量用于存储主进程ID和退出标志
DWORD g_MainProcessId = 0;
volatile BOOL g_ForceExit = FALSE;

// 全局变量，用于终止线程
volatile BOOL g_StopDelayThread = FALSE;
DWORD g_MainThreadId = 0;
HANDLE g_DelayThreadHandle = NULL;

// 线程函数参数结构
struct DelayThreadParams {
    int delayTime;
    volatile BOOL* pStopFlag;
    BOOL completed;
    DWORD startTick;
};

// 主进程退出检测线程函数
DWORD WINAPI MonitorProcessThread(LPVOID lpParam) {
    DWORD processId = *((DWORD*)lpParam);
    
    while (!g_ForceExit) {
        // 检查进程是否还在运行
        HANDLE hProcess = OpenProcess(PROCESS_QUERY_INFORMATION, FALSE, processId);
        if (hProcess == NULL) {
            // 进程已不存在，设置退出标志
            g_ForceExit = TRUE;
            break;
        }
        
        // 检查进程状态
        DWORD exitCode = 0;
        if (GetExitCodeProcess(hProcess, &exitCode) && exitCode != STILL_ACTIVE) {
            CloseHandle(hProcess);
            g_ForceExit = TRUE;
            break;
        }
        
        CloseHandle(hProcess);
        Sleep(100); // 每100ms检查一次
    }
    
    return 0;
}

// 初始化主进程监控
void InitProcessMonitor() {
    if (g_MainProcessId == 0) {
        g_MainProcessId = GetCurrentProcessId();
        g_ForceExit = FALSE;
        
        // 创建监控线程
        HANDLE hThread = CreateThread(NULL, 0, MonitorProcessThread, &g_MainProcessId, 0, NULL);
        if (hThread) {
            // 设置线程为低优先级
            SetThreadPriority(hThread, THREAD_PRIORITY_BELOW_NORMAL);
            // 设置线程为分离状态，避免资源泄漏
            CloseHandle(hThread);
        }
    }
}

// 检查是否收到退出请求
bool IsExitRequested() {
    return g_ForceExit == TRUE;
}

// 全局变量
std::set<std::string> g_excludedControls = { "按钮" };  // 默认排除按钮控件
std::string g_defaultConfigDir = "data";   // 默认配置文件目录

// 控件类型枚举
enum ControlType {
    CTRL_UNKNOWN = 0,
    CTRL_EDIT = 1,       // 编辑框
    CTRL_COMBOBOX = 2,   // 组合框
    CTRL_CHECKBOX = 3,   // 复选框
    CTRL_RADIOBUTTON = 4,// 单选框
    CTRL_LISTBOX = 5,    // 列表框
    CTRL_DATETIME = 6,   // 日期时间框
    CTRL_LISTVIEW = 7,   // 列表视图
    CTRL_TREEVIEW = 8    // 树视图
};

// 简单的字符串分割函数
std::vector<std::string> split(const std::string& str, char delimiter) {
    std::vector<std::string> tokens;
    std::string token;
    std::istringstream tokenStream(str);
    while (std::getline(tokenStream, token, delimiter)) {
        if (!token.empty()) {
            tokens.push_back(token);
        }
    }
    return tokens;
}

// 简单的字符串修剪函数（去除首尾空格）
std::string trim(const std::string& str) {
    size_t first = str.find_first_not_of(" \t\n\r");
    if (first == std::string::npos) {
        return "";
    }
    size_t last = str.find_last_not_of(" \t\n\r");
    return str.substr(first, (last - first + 1));
}

// 确保目录存在
bool ensureDirectoryExists(const std::string& path) {
    size_t pos = 0;
    std::string dir;
    
    // 分割路径并逐级创建目录
    while ((pos = path.find_first_of("\\/", pos)) != std::string::npos) {
        dir = path.substr(0, pos++);
        if (!dir.empty() && dir.back() != ':') {  // 跳过驱动器前缀 (C:)
            if (_mkdir(dir.c_str()) != 0 && errno != EEXIST) {
                return false;  // 创建目录失败
            }
        }
    }
    return true;
}

// 获取默认配置文件路径
std::string getDefaultConfigPath() {
    char buffer[MAX_PATH];
    GetModuleFileNameA(NULL, buffer, MAX_PATH);
    
    std::string path(buffer);
    size_t lastSlash = path.find_last_of("\\/");
    if (lastSlash != std::string::npos) {
        path = path.substr(0, lastSlash + 1);
    }
    
    std::string configPath = path + g_defaultConfigDir;
    ensureDirectoryExists(configPath);  // 确保目录存在
    return configPath + "\\setsoft.ini";
}

// 获取控件类名
std::string getClassName(HWND hwnd) {
    char className[256] = {0};
    GetClassNameA(hwnd, className, sizeof(className));
    return std::string(className);
}

// 获取控件显示文本
std::string getWindowText(HWND hwnd) {
    int length = GetWindowTextLengthA(hwnd);
    if (length <= 0) {
        return "";
    }
    
    std::vector<char> buffer(length + 1);
    GetWindowTextA(hwnd, buffer.data(), length + 1);
    return std::string(buffer.data());
}

// 获取控件ID
int getControlId(HWND hwnd) {
    return GetDlgCtrlID(hwnd);
}

// 判断控件类型
ControlType getControlType(HWND hwnd) {
    std::string className = getClassName(hwnd);
    std::transform(className.begin(), className.end(), className.begin(), 
                  [](unsigned char c){ return std::tolower(c); });
    
    if (className == "edit") {
        return CTRL_EDIT;
    } else if (className == "combobox") {
        return CTRL_COMBOBOX;
    } else if (className == "button") {
        LONG style = GetWindowLongA(hwnd, GWL_STYLE);
        if ((style & BS_CHECKBOX) || (style & BS_AUTOCHECKBOX)) {
            return CTRL_CHECKBOX;
        } else if ((style & BS_RADIOBUTTON) || (style & BS_AUTORADIOBUTTON)) {
            return CTRL_RADIOBUTTON;
        }
    } else if (className == "listbox") {
        return CTRL_LISTBOX;
    } else if (className == "systreeview32") {
        return CTRL_TREEVIEW;
    } else if (className == "syslistview32") {
        return CTRL_LISTVIEW;
    } else if (className == "sysdatetimepick32") {
        return CTRL_DATETIME;
    }
    
    return CTRL_UNKNOWN;
}

// 获取控件的唯一名称（优先使用ID）
std::string getControlName(HWND hwnd) {
    int id = getControlId(hwnd);
    if (id > 0) {
        return "控件" + std::to_string(id);
    }
    
    // 尝试获取控件文本
    std::string text = getWindowText(hwnd);
    if (!text.empty()) {
        return text;
    }
    
    // 使用内存地址作为最后的备选
    std::stringstream ss;
    ss << "控件_" << hwnd;
    return ss.str();
}

// 获取控件的值
std::string getControlValue(HWND hwnd) {
    ControlType type = getControlType(hwnd);
    std::string value;
    
    switch (type) {
        case CTRL_EDIT: {
            value = getWindowText(hwnd);
            break;
        }
        case CTRL_COMBOBOX: {
            int index = SendMessageA(hwnd, CB_GETCURSEL, 0, 0);
            if (index != CB_ERR) {
                int textLen = SendMessageA(hwnd, CB_GETLBTEXTLEN, index, 0);
                if (textLen > 0) {
                    std::vector<char> buffer(textLen + 1);
                    SendMessageA(hwnd, CB_GETLBTEXT, index, (LPARAM)buffer.data());
                    value = std::string(buffer.data());
                } else {
                    value = getWindowText(hwnd);
                }
            } else {
                value = getWindowText(hwnd);
            }
            break;
        }
        case CTRL_CHECKBOX:
        case CTRL_RADIOBUTTON: {
            LRESULT state = SendMessageA(hwnd, BM_GETCHECK, 0, 0);
            value = (state == BST_CHECKED) ? "1" : "0";
            break;
        }
        case CTRL_LISTBOX: {
            int count = SendMessageA(hwnd, LB_GETCOUNT, 0, 0);
            std::vector<std::string> selected;
            
            for (int i = 0; i < count; i++) {
                if (SendMessageA(hwnd, LB_GETSEL, i, 0) > 0) {
                    int textLen = SendMessageA(hwnd, LB_GETTEXTLEN, i, 0);
                    if (textLen > 0) {
                        std::vector<char> buffer(textLen + 1);
                        SendMessageA(hwnd, LB_GETTEXT, i, (LPARAM)buffer.data());
                        selected.push_back(std::string(buffer.data()));
                    }
                }
            }
            
            if (!selected.empty()) {
                for (size_t i = 0; i < selected.size(); i++) {
                    if (i > 0) value += ",";
                    value += selected[i];
                }
            }
            break;
        }
        case CTRL_DATETIME: {
            SYSTEMTIME st;
            if (SendMessageA(hwnd, DTM_GETSYSTEMTIME, 0, (LPARAM)&st)) {
                char buffer[128];
                sprintf_s(buffer, "%04d-%02d-%02d %02d:%02d:%02d", 
                        st.wYear, st.wMonth, st.wDay, st.wHour, st.wMinute, st.wSecond);
                value = buffer;
            }
            break;
        }
        default:
            value = getWindowText(hwnd);
            break;
    }
    
    return value;
}

// 设置控件的值
bool setControlValue(HWND hwnd, const std::string& value) {
    ControlType type = getControlType(hwnd);
    
    switch (type) {
        case CTRL_EDIT: {
            SetWindowTextA(hwnd, value.c_str());
            return true;
        }
        case CTRL_COMBOBOX: {
            // 尝试查找匹配项
            int count = SendMessageA(hwnd, CB_GETCOUNT, 0, 0);
            for (int i = 0; i < count; i++) {
                int textLen = SendMessageA(hwnd, CB_GETLBTEXTLEN, i, 0);
                if (textLen > 0) {
                    std::vector<char> buffer(textLen + 1);
                    SendMessageA(hwnd, CB_GETLBTEXT, i, (LPARAM)buffer.data());
                    if (value == std::string(buffer.data())) {
                        SendMessageA(hwnd, CB_SETCURSEL, i, 0);
                        return true;
                    }
                }
            }
            
            // 如果没找到匹配项但是组合框是可编辑的，直接设置文本
            SetWindowTextA(hwnd, value.c_str());
            return true;
        }
        case CTRL_CHECKBOX:
        case CTRL_RADIOBUTTON: {
            bool checked = (value == "1" || value == "true" || value == "yes");
            SendMessageA(hwnd, BM_SETCHECK, checked ? BST_CHECKED : BST_UNCHECKED, 0);
            return true;
        }
        case CTRL_LISTBOX: {
            // 清除所有选择
            SendMessageA(hwnd, LB_SETSEL, FALSE, -1);
            
            // 分割多选值
            std::vector<std::string> items = split(value, ',');
            int count = SendMessageA(hwnd, LB_GETCOUNT, 0, 0);
            
            for (const auto& item : items) {
                std::string trimmedItem = trim(item);
                for (int i = 0; i < count; i++) {
                    int textLen = SendMessageA(hwnd, LB_GETTEXTLEN, i, 0);
                    if (textLen > 0) {
                        std::vector<char> buffer(textLen + 1);
                        SendMessageA(hwnd, LB_GETTEXT, i, (LPARAM)buffer.data());
                        if (trimmedItem == std::string(buffer.data())) {
                            SendMessageA(hwnd, LB_SETSEL, TRUE, i);
                            break;
                        }
                    }
                }
            }
            return true;
        }
        case CTRL_DATETIME: {
            // 尝试解析日期时间格式 YYYY-MM-DD HH:MM:SS
            SYSTEMTIME st = {0};
            if (sscanf_s(value.c_str(), "%hd-%hd-%hd %hd:%hd:%hd", 
                    &st.wYear, &st.wMonth, &st.wDay, &st.wHour, &st.wMinute, &st.wSecond) >= 3) {
                SendMessageA(hwnd, DTM_SETSYSTEMTIME, 0, (LPARAM)&st);
                return true;
            }
            return false;
        }
        default:
            // 对于不支持的控件类型，尝试设置文本
            SetWindowTextA(hwnd, value.c_str());
            return true;
    }
}

// 保存配置到文件
bool saveConfigToFile(const std::string& filePath, const std::map<std::string, std::string>& data) {
    try {
        // 确保目录存在
        std::string path = filePath;
        size_t lastSlash = path.find_last_of("\\/");
        if (lastSlash != std::string::npos) {
            ensureDirectoryExists(path.substr(0, lastSlash));
        }
        
        std::ofstream file(filePath.c_str());
        if (!file.is_open()) {
            return false;
        }
        
        for (const auto& pair : data) {
            file << pair.first << "=" << pair.second << std::endl;
        }
        
        file.close();
        return true;
    }
    catch (...) {
        return false;
    }
}

// 从文件加载配置
bool loadConfigFromFile(const std::string& filePath, std::map<std::string, std::string>& data) {
    try {
        std::ifstream file(filePath.c_str());
        if (!file.is_open()) {
            return false;
        }
        
        std::string line;
        while (std::getline(file, line)) {
            line = trim(line);
            if (line.empty() || line[0] == '#' || line[0] == ';') {
                continue;  // 跳过空行和注释
            }
            
            size_t pos = line.find('=');
            if (pos != std::string::npos) {
                std::string key = trim(line.substr(0, pos));
                std::string value = trim(line.substr(pos + 1));
                data[key] = value;
            }
        }
        
        file.close();
        return true;
    }
    catch (...) {
        return false;
    }
}

// 递归枚举窗口中的所有控件
BOOL CALLBACK EnumChildProc(HWND hwnd, LPARAM lParam) {
    auto* controlsMap = reinterpret_cast<std::map<std::string, HWND>*>(lParam);
    
    // 获取控件名称
    std::string name = getControlName(hwnd);
    std::string className = getClassName(hwnd);
    
    // 检查是否应该排除此控件
    bool shouldExclude = false;
    
    // 排除所有按钮控件
    if (className == "Button") {
        // 检查是否为普通按钮（非复选框或单选框）
        LONG style = GetWindowLongA(hwnd, GWL_STYLE);
        if (!((style & BS_CHECKBOX) || (style & BS_AUTOCHECKBOX) || 
              (style & BS_RADIOBUTTON) || (style & BS_AUTORADIOBUTTON))) {
            shouldExclude = true;
        }
    }
    
    // 检查控件名称是否包含关键字（例如 "按钮"）
    for (const auto& keyword : g_excludedControls) {
        if (name.find(keyword) != std::string::npos) {
            shouldExclude = true;
            break;
        }
    }
    
    if (!shouldExclude) {
        (*controlsMap)[name] = hwnd;
    }
    
    return TRUE;  // 继续枚举
}

/**
 * 简化的保存配置函数 - 只需要配置文件路径和父窗口句柄
 */
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    try {
        // 处理配置文件路径
        std::string filePath;
        if (configFile && configFile[0] != '\0') {
            filePath = configFile;
        } else {
            filePath = getDefaultConfigPath();
        }
        
        // 获取窗口句柄
        HWND parentHwnd = (HWND)parentWindow;
        if (!parentHwnd) {
            parentHwnd = GetActiveWindow();
        }
        
        if (!parentHwnd) {
            return 0;  // 无法获取有效窗口
        }
        
        // 加载现有配置
        std::map<std::string, std::string> configData;
        loadConfigFromFile(filePath, configData);  // 即使加载失败也继续
        
        // 枚举窗口中的所有控件
        std::map<std::string, HWND> controls;
        EnumChildWindows(parentHwnd, EnumChildProc, (LPARAM)&controls);
        
        // 保存所有控件值到配置
        for (const auto& pair : controls) {
            const std::string& name = pair.first;
            HWND hwnd = pair.second;
            
            std::string value = getControlValue(hwnd);
            configData[name] = value;
        }
        
        // 保存到文件
        if (saveConfigToFile(filePath, configData)) {
            return static_cast<int>(controls.size());  // 返回保存的控件数量
        }
        
        return 0;  // 保存失败
    }
    catch (...) {
        return 0;
    }
}

/**
 * 简化的读取配置函数 - 只需要配置文件路径和父窗口句柄
 */
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    try {
        // 处理配置文件路径
        std::string filePath;
        if (configFile && configFile[0] != '\0') {
            filePath = configFile;
        } else {
            filePath = getDefaultConfigPath();
        }
        
        // 获取窗口句柄
        HWND parentHwnd = (HWND)parentWindow;
        if (!parentHwnd) {
            parentHwnd = GetActiveWindow();
        }
        
        if (!parentHwnd) {
            return 0;  // 无法获取有效窗口
        }
        
        // 加载配置数据
        std::map<std::string, std::string> configData;
        if (!loadConfigFromFile(filePath, configData)) {
            return 0;  // 读取配置文件失败
        }
        
        // 枚举窗口中的所有控件
        std::map<std::string, HWND> controls;
        EnumChildWindows(parentHwnd, EnumChildProc, (LPARAM)&controls);
        
        // 设置控件值
        int count = 0;
        for (const auto& pair : controls) {
            const std::string& name = pair.first;
            HWND hwnd = pair.second;
            
            auto it = configData.find(name);
            if (it != configData.end()) {
                if (setControlValue(hwnd, it->second)) {
                    count++;
                }
            }
        }
        
        return count;  // 返回成功设置的控件数量
    }
    catch (...) {
        return 0;
    }
}

// 检查是否为易语言窗口的辅助函数
bool IsELanguageWindow(HWND hwnd) {
    char className[256] = {0};
    GetClassNameA(hwnd, className, sizeof(className));
    
    // 易语言主窗口类名特征
    if (strstr(className, "EFormDesign") != NULL ||
        strstr(className, "EForm") != NULL ||
        strstr(className, "ElangWindow") != NULL) {
        return true;
    }
    
    // 检查窗口标题
    char title[256] = {0};
    GetWindowTextA(hwnd, title, sizeof(title));
    if (strstr(title, "易语言") != NULL || 
        strstr(title, "E语言") != NULL) {
        return true;
    }
    
    return false;
}

// 查找当前进程的所有易语言窗口
void FindELanguageWindows(HWND* windows, int& count, int maxCount) {
    count = 0;
    DWORD currentPid = GetCurrentProcessId();
    
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        if (!IsWindowVisible(hwnd)) return TRUE;
        
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        
        auto params = (std::pair<HWND*, std::pair<int&, int>>*)lParam;
        HWND* windows = params->first;
        int& count = params->second.first;
        int maxCount = params->second.second;
        
        if (pid == GetCurrentProcessId()) {
            // 尝试识别是否为易语言窗口
            if (IsELanguageWindow(hwnd) || (GetWindowLongA(hwnd, GWL_STYLE) & WS_CAPTION)) {
                if (count < maxCount) {
                    windows[count++] = hwnd;
                }
            }
        }
        return TRUE;
    }, (LPARAM)&std::make_pair(windows, std::make_pair(std::ref(count), maxCount)));
}

// 窗口钩子过程
LRESULT CALLBACK WindowProc(int nCode, WPARAM wParam, LPARAM lParam) {
    if (nCode >= 0) {
        if (wParam == WM_DESTROY || wParam == WM_CLOSE || wParam == WM_QUIT) {
            // 收到窗口关闭消息，设置停止标志
            g_StopDelayThread = TRUE;
            
            // 如果线程句柄有效，等待其结束
            if (g_DelayThreadHandle) {
                // 给线程200ms时间自行退出
                if (WaitForSingleObject(g_DelayThreadHandle, 200) == WAIT_TIMEOUT) {
                    // 如果线程没有及时退出，强制终止它
                    TerminateThread(g_DelayThreadHandle, 0);
                }
                CloseHandle(g_DelayThreadHandle);
                g_DelayThreadHandle = NULL;
            }
        }
    }
    
    return CallNextHookEx(NULL, nCode, wParam, lParam);
}

// 延时线程函数
unsigned __stdcall DelayThreadProc(void* pvParam) {
    DelayThreadParams* pParams = (DelayThreadParams*)pvParam;
    DWORD endTime = pParams->startTick + pParams->delayTime;
    
    // 保存当前窗口信息
    HWND mainWindow = GetForegroundWindow();
    HWND lastActiveWindow = NULL;
    DWORD lastCheckTime = GetTickCount();
    
    // 寻找并记录进程中的所有顶级窗口
    std::vector<HWND> processWindows;
    EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
        if (!IsWindowVisible(hwnd)) return TRUE;
        
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        if (pid == GetCurrentProcessId()) {
            std::vector<HWND>* pWindows = (std::vector<HWND>*)lParam;
            pWindows->push_back(hwnd);
        }
        return TRUE;
    }, (LPARAM)&processWindows);
    
    while (GetTickCount() < endTime) {
        // 检查停止标志
        if (*(pParams->pStopFlag)) {
            break;
        }
        
        // 处理窗口消息
        MSG msg;
        while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
            if (msg.message == WM_QUIT || msg.message == WM_CLOSE || msg.message == WM_DESTROY) {
                *(pParams->pStopFlag) = TRUE;
                break;
            }
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        // 定期检查窗口状态
        DWORD currentTime = GetTickCount();
        if (currentTime - lastCheckTime > 50) {
            lastCheckTime = currentTime;
            
            // 检查之前记录的所有窗口是否都已关闭
            bool allWindowsClosed = true;
            for (HWND hwnd : processWindows) {
                if (IsWindow(hwnd) && IsWindowVisible(hwnd)) {
                    allWindowsClosed = false;
                    break;
                }
            }
            
            if (allWindowsClosed && !processWindows.empty()) {
                // 所有窗口都已关闭，终止线程
                *(pParams->pStopFlag) = TRUE;
                break;
            }
            
            // 检查主窗口状态
            if (mainWindow && !IsWindow(mainWindow)) {
                // 主窗口已关闭
                *(pParams->pStopFlag) = TRUE;
                break;
            }
            
            // 获取当前活动窗口
            HWND activeWindow = GetForegroundWindow();
            if (activeWindow != lastActiveWindow) {
                lastActiveWindow = activeWindow;
                
                // 检查活动窗口是否变化到其他程序
                DWORD activePid = 0;
                if (activeWindow) {
                    GetWindowThreadProcessId(activeWindow, &activePid);
                    
                    // 如果活动窗口变化到其他程序，并且我们的主窗口已经不可见
                    if (activePid != GetCurrentProcessId() && mainWindow && 
                        (!IsWindow(mainWindow) || !IsWindowVisible(mainWindow))) {
                        // 再次检查是否有任何可见窗口
                        bool hasVisibleWindow = false;
                        EnumWindows([](HWND hwnd, LPARAM lParam) -> BOOL {
                            DWORD pid = 0;
                            GetWindowThreadProcessId(hwnd, &pid);
                            if (pid == GetCurrentProcessId() && IsWindowVisible(hwnd)) {
                                *(bool*)lParam = true;
                                return FALSE;
                            }
                            return TRUE;
                        }, (LPARAM)&hasVisibleWindow);
                        
                        if (!hasVisibleWindow) {
                            *(pParams->pStopFlag) = TRUE;
                            break;
                        }
                    }
                }
            }
        }
        
        // 内存优化
        static int loopCount = 0;
        if (++loopCount % 500 == 0) {
            SetProcessWorkingSetSize(GetCurrentProcess(), (SIZE_T)-1, (SIZE_T)-1);
        }
        
        Sleep(1); // 短暂休眠，减轻CPU负担
    }
    
    // 标记延时完成
    pParams->completed = TRUE;
    return 0;
}

/**
 * 程序延时函数 - 终极优化版
 * 
 * @param delayTime 延时毫秒数
 * @return 成功返回1，失败返回0
 */
MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    // 参数检查
    if (delayTime <= 0) {
        Sleep(1);
        return 1;
    }
    
    // 保存当前优先级并调整为低优先级，减少资源消耗
    HANDLE hThread = GetCurrentThread();
    int oldThreadPriority = GetThreadPriority(hThread);
    SetThreadPriority(hThread, THREAD_PRIORITY_BELOW_NORMAL);
    
    // 获取进程句柄
    HANDLE hProcess = GetCurrentProcess();
    
    // ====== 内存优化初始化 ======
    
    // 启动强力优化 - 在延迟开始前一次性执行的优化
    SetProcessWorkingSetSize(hProcess, (SIZE_T)-1, (SIZE_T)-1);
    EmptyWorkingSet(hProcess);
    
    // 优化堆内存
    HANDLE hHeap = GetProcessHeap();
    if (hHeap) {
        HeapCompact(hHeap, 0);
    }
    
    // 强制执行一次垃圾回收
    ForceGarbageCollection();
    
    // 优化计时器初始化
    DWORD lastLightOptimizeTime = GetTickCount();
    DWORD lastMediumOptimizeTime = lastLightOptimizeTime;
    DWORD lastHeavyOptimizeTime = lastLightOptimizeTime;
    
    // ====== 窗口监控系统初始化 ======
    
    // 获取主窗口句柄
    HWND hMainWindow = GetActiveWindow();
    if (!hMainWindow || !IsWindow(hMainWindow)) {
        hMainWindow = GetForegroundWindow();
    }
    
    // 验证窗口是否属于当前进程
    if (hMainWindow) {
        DWORD windowPid = 0;
        GetWindowThreadProcessId(hMainWindow, &windowPid);
        if (windowPid != GetCurrentProcessId()) {
            hMainWindow = NULL;
        }
    }
    
    // 存储当前进程的所有可见窗口
    const int MAX_WINDOWS = 64;
    HWND monitoredWindows[MAX_WINDOWS];
    int windowCount = 0;
    
    // 手动枚举窗口
    HWND hwnd = GetTopWindow(NULL);
    while (hwnd && windowCount < MAX_WINDOWS) {
        if (IsWindowVisible(hwnd)) {
            DWORD pid = 0;
            GetWindowThreadProcessId(hwnd, &pid);
            if (pid == GetCurrentProcessId()) {
                monitoredWindows[windowCount++] = hwnd;
            }
        }
        hwnd = GetNextWindow(hwnd, GW_HWNDNEXT);
    }
    
    // ====== 高精度定时器初始化 ======
    
    // 使用高精度性能计数器进行精确延时
    LARGE_INTEGER frequency, startCount, currentCount;
    QueryPerformanceFrequency(&frequency);
    QueryPerformanceCounter(&startCount);
    
    // 计算结束时间点
    LONGLONG endCount = startCount.QuadPart + (delayTime * frequency.QuadPart / 1000);
    
    // 创建可等待的同步对象，用于高效休眠
    HANDLE hTimer = CreateWaitableTimer(NULL, TRUE, NULL);
    HANDLE hEvent = CreateEvent(NULL, TRUE, FALSE, NULL);
    
    // 性能统计初始化
    DWORD optimizationCount[3] = {0}; // 轻、中、重三级优化计数
    
    // 窗口关闭标志
    bool windowClosed = false;
    
    // 状态跟踪变量
    DWORD lastWindowCheckTime = GetTickCount();
    HWND lastActiveWindow = GetForegroundWindow();
    
    // ====== 主延时循环 ======
    while (true) {
        // 消息处理
        MSG msg;
        while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
            // 检测退出消息
            if (msg.message == WM_QUIT || msg.message == WM_CLOSE || msg.message == WM_DESTROY) {
                windowClosed = true;
                break;
            }
            
            // 处理消息
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        // 窗口状态检测
        DWORD currentTime = GetTickCount();
        if (currentTime - lastWindowCheckTime >= 50) { // 每50ms检查一次
            lastWindowCheckTime = currentTime;
            
            // 检查主窗口状态
            if (hMainWindow && (!IsWindow(hMainWindow) || !IsWindowVisible(hMainWindow))) {
                windowClosed = true;
            }
            
            // 检查活动窗口变化
            HWND activeWindow = GetForegroundWindow();
            if (activeWindow != lastActiveWindow) {
                lastActiveWindow = activeWindow;
                
                // 如果焦点转移到其他进程，检查我们的窗口是否都关闭了
                DWORD activePid = 0;
                if (activeWindow) {
                    GetWindowThreadProcessId(activeWindow, &activePid);
                    
                    if (activePid != GetCurrentProcessId()) {
                        // 检查我们是否还有可见窗口
                        bool hasVisibleWindow = false;
                        for (int i = 0; i < windowCount; i++) {
                            if (IsWindow(monitoredWindows[i]) && IsWindowVisible(monitoredWindows[i])) {
                                hasVisibleWindow = true;
                                break;
                            }
                        }
                        
                        // 如果没有可见窗口了，可能意味着应用程序已关闭
                        if (!hasVisibleWindow && windowCount > 0) {
                            windowClosed = true;
                        }
                    }
                }
            }
            
            // 全面窗口状态检查
            if (!windowClosed && windowCount > 0) {
                // 检查是否所有监控的窗口都已关闭
                bool allClosed = true;
                for (int i = 0; i < windowCount; i++) {
                    if (IsWindow(monitoredWindows[i]) && IsWindowVisible(monitoredWindows[i])) {
                        allClosed = false;
                        break;
                    }
                }
                
                if (allClosed) {
                    windowClosed = true;
                }
            }
        }
        
        // 检查是否退出循环
        if (windowClosed) {
            break;
        }
        
        // 检查延时是否结束
        QueryPerformanceCounter(&currentCount);
        if (currentCount.QuadPart >= endCount) {
            break;
        }
        
        // ====== 内存优化 ======
        
        // 1. 轻量级优化 - 每100ms
        if (currentTime - lastLightOptimizeTime >= 100) {
            // 轻量级工作集调整
            SetProcessWorkingSetSize(hProcess, (SIZE_T)-1, (SIZE_T)-1);
            
            lastLightOptimizeTime = currentTime;
            optimizationCount[0]++;
        }
        
        // 2. 中量级优化 - 每500ms
        if (currentTime - lastMediumOptimizeTime >= 500) {
            // 压缩主堆
            if (hHeap) {
                HeapCompact(hHeap, 0);
            }
            
            lastMediumOptimizeTime = currentTime;
            optimizationCount[1]++;
        }
        
        // 3. 重量级优化 - 每2000ms
        if (currentTime - lastHeavyOptimizeTime >= 2000) {
            // 释放不需要的页面
            EmptyWorkingSet(hProcess);
            
            // 强制垃圾回收
            ForceGarbageCollection();
            
            lastHeavyOptimizeTime = currentTime;
            optimizationCount[2]++;
        }
        
        // ====== 智能休眠策略 ======
        
        // 计算剩余时间
        QueryPerformanceCounter(&currentCount);
        LONGLONG remainingCount = endCount - currentCount.QuadPart;
        DWORD remainingMs = (DWORD)(remainingCount * 1000 / frequency.QuadPart);
        
        // 基于剩余时间的自适应休眠策略
        if (remainingMs > 500) {
            // 长时间剩余 - 使用可等待计时器，精确且省电
            LARGE_INTEGER dueTime;
            dueTime.QuadPart = -10000LL * min(remainingMs / 2, 100); // 以100纳秒为单位，负值表示相对时间
            if (hTimer) {
                SetWaitableTimer(hTimer, &dueTime, 0, NULL, NULL, FALSE);
                WaitForSingleObject(hTimer, 100);
            } else {
                Sleep(min(remainingMs / 2, 100));
            }
        } else if (remainingMs > 100) {
            // 中等时间剩余 - 平衡响应性和效率
            Sleep(10);
        } else if (remainingMs > 20) {
            // 短时间剩余 - 提高精度
            Sleep(1);
        } else {
            // 极短时间剩余 - 使用自旋等待提高精度
            YieldProcessor(); // 让出处理器时间片但不休眠
        }
    }
    
    // ====== 终止阶段清理和优化 ======
    
    // 关闭同步句柄
    if (hTimer) CloseHandle(hTimer);
    if (hEvent) CloseHandle(hEvent);
    
    // 最终内存优化
    ForceGarbageCollection();
    EmptyWorkingSet(hProcess);
    SetProcessWorkingSetSize(hProcess, (SIZE_T)-1, (SIZE_T)-1);
    
    // 恢复线程优先级
    SetThreadPriority(hThread, oldThreadPriority);
    
    // 如果窗口被关闭，终止进程
    if (windowClosed) {
        // 终止优化 - 进行最终的资源清理
        EmptyWorkingSet(hProcess);
        SetProcessWorkingSetSize(hProcess, 1, 1); // 设置最小工作集
        
        // 终止所有线程并退出进程
        ExitProcess(0);
    }
    
    return 1;
}

// 辅助函数 - 强制垃圾回收
void ForceGarbageCollection() {
    // 清理文件缓存
    FlushFileBuffers(NULL);
    
    // 尝试回收更多内存资源
    // 这里使用一些技巧强制系统回收内存
    for (int i = 0; i < 3; i++) {
        void* p = VirtualAlloc(NULL, 256 * 1024 * 1024, MEM_RESERVE, PAGE_NOACCESS);
        if (p) {
            VirtualFree(p, 0, MEM_RELEASE);
        }
    }
    
    // 请求系统进行全面内存回收
    SetProcessWorkingSetSize(GetCurrentProcess(), (SIZE_T)-1, (SIZE_T)-1);
    EmptyWorkingSet(GetCurrentProcess());
}

// DLL入口点
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // DLL被加载时的初始化代码
        g_MainProcessId = GetCurrentProcessId();
        g_ForceExit = FALSE;
        g_MainThreadId = GetCurrentThreadId();
        g_StopDelayThread = FALSE;
        g_DelayThreadHandle = NULL;
        break;
    case DLL_THREAD_ATTACH:
        // 新线程创建时的初始化代码
        break;
    case DLL_THREAD_DETACH:
        // 线程退出时的清理代码
        break;
    case DLL_PROCESS_DETACH:
        // DLL被卸载时的清理代码
        g_ForceExit = TRUE; // 设置退出标志
        g_StopDelayThread = TRUE;
        
        // 确保线程被终止
        if (g_DelayThreadHandle) {
            if (WaitForSingleObject(g_DelayThreadHandle, 200) == WAIT_TIMEOUT) {
                TerminateThread(g_DelayThreadHandle, 0);
            }
            CloseHandle(g_DelayThreadHandle);
            g_DelayThreadHandle = NULL;
        }
        break;
    }
    return TRUE;
} 
@echo off
chcp 65001 >nul
echo Starting Easy Language DLL compilation...

:: Find Visual Studio
set "VCPATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2022\Community"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2022\Professional"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2019\Community"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools"
) else (
    echo Visual Studio not found!
    pause
    exit /b 1
    )
    
echo Visual Studio compiler not found. Searching...
echo Found Visual Studio: %VCPATH%

    echo Setting up environment...
call "%VCPATH%\VC\Auxiliary\Build\vcvars32.bat" >nul 2>&1
if errorlevel 1 (
    echo Failed to setup Visual Studio environment
    pause
    exit /b 1
    )
    
echo Visual Studio environment setup successful

:: Create build directory
if not exist "build" mkdir build

echo Creating DEF file...
echo LIBRARY MyDll > src\mydll.def
echo EXPORTS >> src\mydll.def
echo     SaveConfig @1 >> src\mydll.def
echo     LoadConfig @2 >> src\mydll.def
echo     ProgramDelay @3 >> src\mydll.def
echo     RemoteCallDLL @4 >> src\mydll.def
echo     GetLastCallResultInt @5 >> src\mydll.def
echo     GetLastCallResultDouble @6 >> src\mydll.def
echo     GetLastCallResultString @7 >> src\mydll.def
echo     GetLastCallResultType @8 >> src\mydll.def
echo     HasLastCallResult @9 >> src\mydll.def

echo Compiling 32-bit DLL with encoding support...
cl /LD /Fe:build\MyDll.dll src\mydll_encoding.cpp src\mydll.def /DMYDLL_EXPORTS /O2 /MT /EHsc >build\compile.log 2>&1

if errorlevel 1 (
    echo Compilation failed
    type build\compile.log
    echo 错误: 编译失败，详细错误信息见上方
) else (
    echo Compilation successful!
    copy build\MyDll.dll . >nul 2>&1
    echo MyDll.dll copied to project root
    echo.
    echo ✅ 编码处理版本DLL编译成功！
    echo 🎯 新功能：智能编码转换，确保文本返回真正的文本
    echo 📁 输出文件：MyDll.dll（根目录）和 build\MyDll.dll
)

echo.
pause 
@echo off
echo MyDll.dll Integrated Build Script
echo ================================
echo Features: RemoteCallDLL + Config + Delay + Debug
echo.

:: 查找Visual Studio
set "VCPATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2022\Community"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2022\Professional"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2019\Community"
) else if exist "C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools"
) else (
    echo ERROR: Visual Studio not found!
    echo Please install Visual Studio 2019 or 2022
    pause
    exit /b 1
)

echo Found Visual Studio: %VCPATH%

:: Setup build environment
echo Setting up build environment...
call "%VCPATH%\VC\Auxiliary\Build\vcvars32.bat" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Cannot setup Visual Studio environment
    pause
    exit /b 1
)

:: Create build directory
if not exist "build" mkdir build

:: Create DEF file
echo Creating function export definitions...
echo LIBRARY MyDll > src\mydll.def
echo EXPORTS >> src\mydll.def
echo     SaveConfig @1 >> src\mydll.def
echo     LoadConfig @2 >> src\mydll.def
echo     ProgramDelay @3 >> src\mydll.def
echo     RemoteCallDLL @4 >> src\mydll.def
echo     GetLastCallResultInt @5 >> src\mydll.def
echo     GetLastCallResultDouble @6 >> src\mydll.def
echo     GetLastCallResultString @7 >> src\mydll.def
echo     GetLastCallResultType @8 >> src\mydll.def
echo     HasLastCallResult @9 >> src\mydll.def
echo     ExtractTextFromResultInt @10 >> src\mydll.def
echo     GetDebugInfo @11 >> src\mydll.def

:: Compile DLL
echo Compiling MyDll.dll...
cl /LD /Fe:build\MyDll.dll src\mydll.cpp src\mydll.def /DMYDLL_EXPORTS /O2 /MT /EHsc >build\compile.log 2>&1

if errorlevel 1 (
    echo ERROR: Compilation failed! See details:
    type build\compile.log
    echo.
    echo Troubleshooting:
    echo 1. Ensure Visual Studio is properly installed
    echo 2. Check source files for syntax errors
    echo 3. Ensure all dependencies are available
    pause
    exit /b 1
) else (
    echo SUCCESS: Compilation completed!

    :: Copy to root directory
    copy build\MyDll.dll . >nul 2>&1
    if errorlevel 1 (
        echo WARNING: Cannot copy to root directory, but compilation succeeded
    ) else (
        echo MyDll.dll copied to root directory
    )

    echo.
    echo BUILD COMPLETED!
    echo ================================
    echo Features:
    echo   - RemoteCallDLL - Remote DLL calling with perfect text support
    echo   - SaveConfig/LoadConfig - Configuration management
    echo   - ProgramDelay - Program delay
    echo   - GetLastCallResult* - Result retrieval functions
    echo   - GetDebugInfo - Debug information
    echo.
    echo Output files:
    echo   - MyDll.dll (root directory)
    echo   - build\MyDll.dll (build directory)
    echo   - build\MyDll.lib (import library)
    echo.
    echo Test suggestion:
    echo   Run your Easy Language test program and test:
    echo   RemoteCallDLL(address, "text_test", "", 0, result, 3, 4096)
    echo   Should now return text content correctly!
)

echo.
pause
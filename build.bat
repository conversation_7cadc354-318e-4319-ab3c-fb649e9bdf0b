@echo off
echo MyDll.dll Build Script
echo ======================

REM Find Visual Studio
set "VCPATH="
if exist "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2022\Community"
) else if exist "C:\Program Files\Microsoft Visual Studio\2022\Professional\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2022\Professional"
) else if exist "C:\Program Files\Microsoft Visual Studio\2019\Community\VC\Auxiliary\Build\vcvars32.bat" (
    set "VCPATH=C:\Program Files\Microsoft Visual Studio\2019\Community"
) else (
    echo ERROR: Visual Studio not found!
    pause
    exit /b 1
)

echo Found Visual Studio: %VCPATH%

REM Setup environment
call "%VCPATH%\VC\Auxiliary\Build\vcvars32.bat" >nul 2>&1

REM Create build directory
if not exist "build" mkdir build

REM Create DEF file
echo LIBRARY MyDll > src\mydll.def
echo EXPORTS >> src\mydll.def
echo     SaveConfig @1 >> src\mydll.def
echo     LoadConfig @2 >> src\mydll.def
echo     ProgramDelay @3 >> src\mydll.def
echo     RemoteCallDLL @4 >> src\mydll.def
echo     GetLastCallResultInt @5 >> src\mydll.def
echo     GetLastCallResultDouble @6 >> src\mydll.def
echo     GetLastCallResultString @7 >> src\mydll.def
echo     GetLastCallResultType @8 >> src\mydll.def
echo     HasLastCallResult @9 >> src\mydll.def
echo     ExtractTextFromResultInt @10 >> src\mydll.def
echo     GetDebugInfo @11 >> src\mydll.def

REM Compile
echo Compiling...
cl /LD /Fe:build\MyDll.dll src\mydll_stable.cpp src\mydll.def /DMYDLL_EXPORTS /O2 /MT /EHsc

if errorlevel 1 (
    echo Compilation failed!
    pause
    exit /b 1
) else (
    echo Compilation successful!
    copy build\MyDll.dll . >nul 2>&1
    echo MyDll.dll ready!
)

pause

#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <wininet.h>
#include <urlmon.h>
#include <stdio.h>
#include <string.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// Global variables
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;
static char g_DebugInfo[1024] = {0};

// Simple and stable RemoteCallDLL
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName, 
                                      const char* parameters, int paramCount, 
                                      void* result, int resultType, int bufferSize)
{
    // Initialize result safely
    if (result) {
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                memset(result, 0, bufferSize);
            }
        } else {
            memset(result, 0, sizeof(int));
        }
    }

    if (!remotePath || !functionName || !result) {
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Invalid parameters");
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;
    
    // Handle remote download
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());
        
        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5) {
                if (bufferSize > 0) {
                    strcpy_s((char*)result, bufferSize, "Download failed");
                }
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
            sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Download failed: %s", remotePath);
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }
    
    // Load DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                sprintf_s((char*)result, bufferSize, "Load failed: %d", error);
            }
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed: %d", error);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "LoadLibrary failed: %s, Error: %d", dllPath, error);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // Get function address
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                sprintf_s((char*)result, bufferSize, "Function not found: %s", functionName);
            }
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found: %s", functionName);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Function '%s' not found in DLL, Error: %d", functionName, error);
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Simple parameter parsing
    DWORD_PTR args[16] = {0};
    int actualParamCount = 0;
    
    if (parameters && strlen(parameters) > 0 && paramCount > 0) {
        char paramsCopy[1024];
        strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);
        
        // Extract parameter value (handle both "int:1" and "整数型：1" formats)
        char* value = paramsCopy;
        if (strstr(paramsCopy, ":") != NULL) {
            value = strstr(paramsCopy, ":") + 1;
            // Skip Chinese colon if present
            if (strstr(paramsCopy, "：") != NULL) {
                value = strstr(paramsCopy, "：") + 3; // Chinese colon is 3 bytes in UTF-8
            }
        }
        
        // Convert to integer
        args[0] = (DWORD_PTR)atoi(value);
        actualParamCount = 1;
    }
    
    // Call function safely
    BOOL callSuccess = FALSE;
    DWORD_PTR functionResult = 0;
    
    __try {
        if (actualParamCount == 0) {
            typedef DWORD_PTR (__stdcall *Func0)();
            Func0 func = (Func0)procAddr;
            functionResult = func();
            callSuccess = TRUE;
        } else if (actualParamCount == 1) {
            typedef DWORD_PTR (__stdcall *Func1)(DWORD_PTR);
            Func1 func = (Func1)procAddr;
            functionResult = func(args[0]);
            callSuccess = TRUE;
        }
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        functionResult = 0;
        callSuccess = FALSE;
    }
    
    // Process result
    if (callSuccess) {
        // Try to extract text from result if it looks like a pointer
        char textResult[1024] = {0};
        BOOL hasText = FALSE;
        
        if (functionResult != 0) {
            __try {
                const char* strPtr = reinterpret_cast<const char*>(functionResult);
                int textLen = strnlen(strPtr, 1024);
                if (textLen > 0 && textLen < 1024) {
                    strncpy_s(textResult, sizeof(textResult), strPtr, textLen);
                    hasText = TRUE;
                }
            } __except(EXCEPTION_EXECUTE_HANDLER) {
                hasText = FALSE;
            }
        }
        
        // Set result based on type
        switch (resultType) {
            case 1: // Integer
                *(int*)result = (int)functionResult;
                break;
            case 2: // Double
                *(double*)result = (double)functionResult;
                g_LastCallResultDouble = (double)functionResult;
                break;
            case 3: // String
            case 5: // Auto
                if (bufferSize > 0) {
                    if (hasText) {
                        strcpy_s((char*)result, bufferSize, textResult);
                    } else {
                        sprintf_s((char*)result, bufferSize, "%d", (int)functionResult);
                    }
                }
                break;
            case 4: // Boolean
                *(BOOL*)result = (functionResult != 0);
                break;
            case 6: // Long integer
                *(long long*)result = (long long)functionResult;
                break;
            default:
                *(int*)result = (int)functionResult;
                break;
        }
        
        // Store to global variables
        if (hasText) {
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult);
            g_LastCallResultType = 2; // Text type
        } else {
            sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", (int)functionResult);
            g_LastCallResultType = 0; // Numeric type
        }
        
        g_LastCallResult = (int)functionResult;
        g_LastCallHasResult = TRUE;
        
        // Debug info
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), 
            "Function: %s, Params: [%s], ParamCount: %d->%d, Result: 0x%X, HasText: %s, Success: Yes", 
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount, 
            (unsigned int)functionResult, hasText ? "Yes" : "No");
    } else {
        // Call failed
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                strcpy_s((char*)result, bufferSize, "Call failed");
            }
        } else {
            *(int*)result = -1;
        }
        
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), 
            "Function: %s, Params: [%s], ParamCount: %d->%d, Success: No", 
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount);
    }

    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);

    return callSuccess ? 1 : 0;
}

// Other API functions
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) { return 1; }
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) { return 0; }
MYDLL_API int __stdcall ProgramDelay(int delayTime) { if (delayTime > 0) Sleep(delayTime); return 1; }
MYDLL_API int __stdcall GetLastCallResultInt() { return g_LastCallResult; }
MYDLL_API double __stdcall GetLastCallResultDouble() { return g_LastCallResultDouble; }
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;
    int len = strlen(g_LastCallResultString);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;
    if (copyLen > 0) memcpy(buffer, g_LastCallResultString, copyLen);
    buffer[copyLen] = '\0';
    return copyLen;
}
MYDLL_API int __stdcall GetLastCallResultType() { return g_LastCallResultType; }
MYDLL_API int __stdcall HasLastCallResult() { return g_LastCallHasResult ? 1 : 0; }
MYDLL_API int __stdcall ExtractTextFromResultInt(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0 || !g_LastCallHasResult) return 0;
    sprintf_s(buffer, bufferSize, "Pointer: %p", (void*)g_LastCallResult);
    return strlen(buffer);
}
MYDLL_API int __stdcall GetDebugInfo(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;
    int len = strlen(g_DebugInfo);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;
    if (copyLen > 0) memcpy(buffer, g_DebugInfo, copyLen);
    buffer[copyLen] = '\0';
    return copyLen;
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    return TRUE;
}

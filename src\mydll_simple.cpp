#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <wininet.h>
#include <urlmon.h>
#include <stdio.h>
#include <string.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// Global variables
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;
static char g_DebugInfo[1024] = {0};

// Chinese parameter parsing function
int ParseChineseParameters(const char* paramDesc, DWORD_PTR* args, int* paramTypes,
                          char** stringParams, int maxParams) {
    if (!paramDesc || strlen(paramDesc) == 0) return 0;

    int paramCount = 0;
    char descCopy[2048];
    strcpy_s(descCopy, sizeof(descCopy), paramDesc);

    // Check if it contains Chinese type indicators
    if (strstr(descCopy, "型：") == NULL && strstr(descCopy, ":") == NULL) {
        return 0; // Not a typed parameter format
    }

    // Split by Chinese comma or regular comma
    char* context = NULL;
    char* token = strtok_s(descCopy, "，,", &context);

    while (token && paramCount < maxParams) {
        // Trim spaces
        while (*token == ' ') token++;
        char* end = token + strlen(token) - 1;
        while (end > token && *end == ' ') *end-- = '\0';

        // Parse different Chinese types
        if (strstr(token, "文本型：") == token) {
            paramTypes[paramCount] = 1; // String
            char* value = token + 9; // Skip "文本型："
            stringParams[paramCount] = _strdup(value);
            args[paramCount] = (DWORD_PTR)stringParams[paramCount];
            paramCount++;
        }
        else if (strstr(token, "整数型：") == token) {
            paramTypes[paramCount] = 0; // Integer
            char* value = token + 9; // Skip "整数型："
            int intVal = atoi(value);
            args[paramCount] = (DWORD_PTR)intVal;
            paramCount++;
        }
        else if (strstr(token, "小数型：") == token) {
            paramTypes[paramCount] = 2; // Float
            char* value = token + 9; // Skip "小数型："
            float floatVal = (float)atof(value);
            args[paramCount] = *(DWORD_PTR*)&floatVal;
            paramCount++;
        }
        else if (strstr(token, "双精度小数型：") == token) {
            paramTypes[paramCount] = 3; // Double
            char* value = token + 15; // Skip "双精度小数型："
            double doubleVal = atof(value);
            args[paramCount] = *(DWORD_PTR*)&doubleVal;
            paramCount++;
        }
        else if (strstr(token, "逻辑型：") == token) {
            paramTypes[paramCount] = 4; // Boolean
            char* value = token + 9; // Skip "逻辑型："
            bool boolVal = (strcmp(value, "真") == 0 || strcmp(value, "1") == 0 || strcmp(value, "true") == 0);
            args[paramCount] = (DWORD_PTR)boolVal;
            paramCount++;
        }
        else if (strstr(token, "字节型：") == token) {
            paramTypes[paramCount] = 5; // Byte
            char* value = token + 9; // Skip "字节型："
            unsigned char byteVal = (unsigned char)atoi(value);
            args[paramCount] = (DWORD_PTR)byteVal;
            paramCount++;
        }
        else if (strstr(token, "短整数型：") == token) {
            paramTypes[paramCount] = 6; // Short
            char* value = token + 12; // Skip "短整数型："
            short shortVal = (short)atoi(value);
            args[paramCount] = (DWORD_PTR)shortVal;
            paramCount++;
        }
        else if (strstr(token, "长整数型：") == token) {
            paramTypes[paramCount] = 7; // Long
            char* value = token + 12; // Skip "长整数型："
            long long longVal = _strtoi64(value, NULL, 10);
            args[paramCount] = (DWORD_PTR)longVal;
            paramCount++;
        }
        else if (strstr(token, "日期时间型：") == token) {
            paramTypes[paramCount] = 8; // DateTime
            char* value = token + 15; // Skip "日期时间型："
            stringParams[paramCount] = _strdup(value);
            args[paramCount] = (DWORD_PTR)stringParams[paramCount];
            paramCount++;
        }
        else if (strstr(token, "字节集：") == token) {
            paramTypes[paramCount] = 9; // Bytes
            char* value = token + 9; // Skip "字节集："
            stringParams[paramCount] = _strdup(value);
            args[paramCount] = (DWORD_PTR)stringParams[paramCount];
            paramCount++;
        }
        else if (strstr(token, "子程序指针：") == token) {
            paramTypes[paramCount] = 10; // Function pointer
            char* value = token + 15; // Skip "子程序指针："
            stringParams[paramCount] = _strdup(value);
            args[paramCount] = (DWORD_PTR)stringParams[paramCount];
            paramCount++;
        }
        // English format support
        else if (strstr(token, "str:") == token) {
            paramTypes[paramCount] = 1;
            char* value = token + 4;
            stringParams[paramCount] = _strdup(value);
            args[paramCount] = (DWORD_PTR)stringParams[paramCount];
            paramCount++;
        }
        else if (strstr(token, "int:") == token) {
            paramTypes[paramCount] = 0;
            char* value = token + 4;
            args[paramCount] = (DWORD_PTR)atoi(value);
            paramCount++;
        }
        else if (strstr(token, "float:") == token) {
            paramTypes[paramCount] = 2;
            char* value = token + 6;
            float floatVal = (float)atof(value);
            args[paramCount] = *(DWORD_PTR*)&floatVal;
            paramCount++;
        }
        else if (strstr(token, "bool:") == token) {
            paramTypes[paramCount] = 4;
            char* value = token + 5;
            bool boolVal = (strcmp(value, "true") == 0 || strcmp(value, "1") == 0);
            args[paramCount] = (DWORD_PTR)boolVal;
            paramCount++;
        }

        token = strtok_s(NULL, "，,", &context);
    }

    return paramCount;
}

// Simple and stable RemoteCallDLL
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName,
                                      const char* parameters, int paramCount,
                                      void* result, int resultType, int bufferSize)
{
    // Initialize result safely
    if (result) {
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                memset(result, 0, bufferSize);
            }
        } else {
            memset(result, 0, sizeof(int));
        }
    }

    if (!remotePath || !functionName || !result) {
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Invalid parameters");
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;

    // Handle remote download
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());

        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5) {
                if (bufferSize > 0) {
                    strcpy_s((char*)result, bufferSize, "Download failed");
                }
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
            sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Download failed: %s", remotePath);
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }

    // Load DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                sprintf_s((char*)result, bufferSize, "Load failed: %d", error);
            }
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed: %d", error);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "LoadLibrary failed: %s, Error: %d", dllPath, error);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Get function address
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                sprintf_s((char*)result, bufferSize, "Function not found: %s", functionName);
            }
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found: %s", functionName);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Function '%s' not found in DLL, Error: %d", functionName, error);
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Enhanced parameter parsing with full Chinese support
    DWORD_PTR args[16] = {0};
    char* stringParams[16] = {0};
    int paramTypes[16] = {0};
    int actualParamCount = 0;

    if (parameters && strlen(parameters) > 0) {
        char paramsCopy[2048];
        strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);

        // Parse Chinese format parameters
        actualParamCount = ParseChineseParameters(paramsCopy, args, paramTypes, stringParams, 16);

        // If no Chinese format detected, use simple parsing
        if (actualParamCount == 0 && paramCount > 0) {
            args[0] = (DWORD_PTR)atoi(parameters);
            paramTypes[0] = 0; // Integer
            actualParamCount = 1;
        }
    }

    // Call function safely
    BOOL callSuccess = FALSE;
    DWORD_PTR functionResult = 0;

    __try {
        if (actualParamCount == 0) {
            typedef DWORD_PTR (__stdcall *Func0)();
            Func0 func = (Func0)procAddr;
            functionResult = func();
            callSuccess = TRUE;
        } else if (actualParamCount == 1) {
            typedef DWORD_PTR (__stdcall *Func1)(DWORD_PTR);
            Func1 func = (Func1)procAddr;
            functionResult = func(args[0]);
            callSuccess = TRUE;
        }
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        functionResult = 0;
        callSuccess = FALSE;
    }

    // Process result
    if (callSuccess) {
        // Try to extract text from result if it looks like a pointer
        char textResult[1024] = {0};
        BOOL hasText = FALSE;

        if (functionResult != 0) {
            __try {
                const char* strPtr = reinterpret_cast<const char*>(functionResult);
                int textLen = strnlen(strPtr, 1024);
                if (textLen > 0 && textLen < 1024) {
                    strncpy_s(textResult, sizeof(textResult), strPtr, textLen);
                    hasText = TRUE;
                }
            } __except(EXCEPTION_EXECUTE_HANDLER) {
                hasText = FALSE;
            }
        }

        // Set result based on type
        switch (resultType) {
            case 1: // Integer
                *(int*)result = (int)functionResult;
                break;
            case 2: // Double
                *(double*)result = (double)functionResult;
                g_LastCallResultDouble = (double)functionResult;
                break;
            case 3: // String
            case 5: // Auto
                if (bufferSize > 0) {
                    if (hasText) {
                        strcpy_s((char*)result, bufferSize, textResult);
                    } else {
                        sprintf_s((char*)result, bufferSize, "%d", (int)functionResult);
                    }
                }
                break;
            case 4: // Boolean
                *(BOOL*)result = (functionResult != 0);
                break;
            case 6: // Long integer
                *(long long*)result = (long long)functionResult;
                break;
            default:
                *(int*)result = (int)functionResult;
                break;
        }

        // Store to global variables
        if (hasText) {
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult);
            g_LastCallResultType = 2; // Text type
        } else {
            sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", (int)functionResult);
            g_LastCallResultType = 0; // Numeric type
        }

        g_LastCallResult = (int)functionResult;
        g_LastCallHasResult = TRUE;

        // Debug info
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Function: %s, Params: [%s], ParamCount: %d->%d, Result: 0x%X, HasText: %s, Success: Yes",
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount,
            (unsigned int)functionResult, hasText ? "Yes" : "No");
    } else {
        // Call failed
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                strcpy_s((char*)result, bufferSize, "Call failed");
            }
        } else {
            *(int*)result = -1;
        }

        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Function: %s, Params: [%s], ParamCount: %d->%d, Success: No",
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount);
    }

    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);

    return callSuccess ? 1 : 0;
}

// Other API functions
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) { return 1; }
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) { return 0; }
MYDLL_API int __stdcall ProgramDelay(int delayTime) { if (delayTime > 0) Sleep(delayTime); return 1; }
MYDLL_API int __stdcall GetLastCallResultInt() { return g_LastCallResult; }
MYDLL_API double __stdcall GetLastCallResultDouble() { return g_LastCallResultDouble; }
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;
    int len = strlen(g_LastCallResultString);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;
    if (copyLen > 0) memcpy(buffer, g_LastCallResultString, copyLen);
    buffer[copyLen] = '\0';
    return copyLen;
}
MYDLL_API int __stdcall GetLastCallResultType() { return g_LastCallResultType; }
MYDLL_API int __stdcall HasLastCallResult() { return g_LastCallHasResult ? 1 : 0; }
MYDLL_API int __stdcall ExtractTextFromResultInt(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0 || !g_LastCallHasResult) return 0;
    sprintf_s(buffer, bufferSize, "Pointer: %p", (void*)g_LastCallResult);
    return strlen(buffer);
}
MYDLL_API int __stdcall GetDebugInfo(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;
    int len = strlen(g_DebugInfo);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;
    if (copyLen > 0) memcpy(buffer, g_DebugInfo, copyLen);
    buffer[copyLen] = '\0';
    return copyLen;
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    return TRUE;
}

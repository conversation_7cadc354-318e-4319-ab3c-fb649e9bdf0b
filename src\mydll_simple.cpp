#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <string>
#include <wininet.h>
#include <urlmon.h>
#include <clocale>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// 全局变量
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;

// 编码转换函数
std::string ConvertToAnsi(const char* input, int length) {
    if (!input || length <= 0) {
        return "Empty data";
    }
    
    // 尝试UTF-8到ANSI转换
    int wideLen = MultiByteToWideChar(CP_UTF8, 0, input, length, NULL, 0);
    if (wideLen > 0) {
        std::wstring unicode(wideLen, L'\0');
        if (MultiByteToWideChar(CP_UTF8, 0, input, length, &unicode[0], wideLen) > 0) {
            int ansiLen = WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, NULL, 0, NULL, NULL);
            if (ansiLen > 0) {
                std::string result(ansiLen - 1, '\0');
                if (WideCharToMultiByte(CP_ACP, 0, unicode.c_str(), -1, &result[0], ansiLen, NULL, NULL) > 0) {
                    return result;
                }
            }
        }
    }
    
    // 直接使用输入
    return std::string(input, length);
}

// 主要的RemoteCallDLL函数
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName, const char* parameters, int paramCount, void* result, int resultType, int bufferSize)
{
    if (!remotePath || !functionName || !result) {
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;
    
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());
        
        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5) {
                strcpy_s((char*)result, bufferSize, "Download failed");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
            g_LastCallResult = -1;
            g_LastCallHasResult = TRUE;
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }
    
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "Load failed");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "Function not found");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    int functionResult = 0;
    std::string textResult;
    bool isTextFunction = false;
    
    // 检查是否为文本函数 - 支持中文
    if (strstr(functionName, "文本") || strstr(functionName, "测试") ||
        strstr(functionName, "Text") || strstr(functionName, "String") || 
        strstr(functionName, "Get") || strstr(functionName, "Version")) {
        isTextFunction = true;
    }
    
    try {
        if (isTextFunction) {
            // 文本函数处理
            if (paramCount == 0) {
                typedef const char* (__stdcall *StringFunc0)();
                StringFunc0 func = (StringFunc0)procAddr;
                const char* rawResult = func();
                
                if (rawResult) {
                    textResult = ConvertToAnsi(rawResult, strlen(rawResult));
                    functionResult = 1;
                } else {
                    textResult = "Null result";
                    functionResult = 0;
                }
            } else if (paramCount == 1) {
                typedef const char* (__stdcall *StringFunc1)(int);
                StringFunc1 func = (StringFunc1)procAddr;
                int param = parameters ? atoi(parameters) : 0;
                const char* rawResult = func(param);
                
                if (rawResult) {
                    textResult = ConvertToAnsi(rawResult, strlen(rawResult));
                    functionResult = 1;
                } else {
                    textResult = "Null result";
                    functionResult = 0;
                }
            } else {
                textResult = "Unsupported parameter count";
                functionResult = 0;
            }
            
            // 设置文本结果
            switch (resultType) {
                case 1:
                    *(int*)result = functionResult;
                    break;
                case 2:
                    *(double*)result = (double)functionResult;
                    break;
                case 3:
                case 5:
                    {
                        int copyLen = min((int)textResult.length(), bufferSize - 1);
                        memcpy((char*)result, textResult.c_str(), copyLen);
                        ((char*)result)[copyLen] = '\0';
                    }
                    break;
                case 4:
                    *(BOOL*)result = (functionResult != 0);
                    break;
                case 6:
                    *(long long*)result = (long long)functionResult;
                    break;
            }
            
            strncpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult.c_str(), _TRUNCATE);
            g_LastCallResult = functionResult;
            g_LastCallResultType = 2;
        } else {
            // 数值函数处理
            if (paramCount == 0) {
                typedef int (__stdcall *IntFunc0)();
                IntFunc0 func = (IntFunc0)procAddr;
                functionResult = func();
            } else if (paramCount == 1) {
                typedef int (__stdcall *IntFunc1)(int);
                IntFunc1 func = (IntFunc1)procAddr;
                int param = parameters ? atoi(parameters) : 0;
                functionResult = func(param);
            } else if (paramCount == 2) {
                typedef int (__stdcall *IntFunc2)(int, int);
                IntFunc2 func = (IntFunc2)procAddr;
                
                int param1 = 0, param2 = 0;
                if (parameters) {
                    char paramsCopy[256];
                    strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);
                    char* comma = strchr(paramsCopy, ',');
                    if (comma) {
                        *comma = '\0';
                        param1 = atoi(paramsCopy);
                        param2 = atoi(comma + 1);
                    } else {
                        param1 = atoi(paramsCopy);
                    }
                }
                
                functionResult = func(param1, param2);
            } else {
                functionResult = -1;
            }
            
            // 设置数值结果
            switch (resultType) {
                case 1:
                    *(int*)result = functionResult;
                    break;
                case 2:
                    *(double*)result = (double)functionResult;
                    break;
                case 3:
                case 5:
                    sprintf_s((char*)result, bufferSize, "%d", functionResult);
                    break;
                case 4:
                    *(BOOL*)result = (functionResult != 0);
                    break;
                case 6:
                    *(long long*)result = (long long)functionResult;
                    break;
            }
            
            sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", functionResult);
            g_LastCallResult = functionResult;
            g_LastCallResultType = 0;
        }
        
        g_LastCallResultDouble = (double)functionResult;
        g_LastCallHasResult = TRUE;
        
    } catch (...) {
        if (resultType == 3 || resultType == 5) {
            strcpy_s((char*)result, bufferSize, "Call exception");
        } else {
            *(int*)result = -1;
        }
        strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Call exception");
        g_LastCallResult = -1;
        g_LastCallHasResult = TRUE;
        g_LastCallResultType = 2;
    }
    
    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);
    
    return 1;
}

// 其他函数
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    return 1;
}

MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    return 0;
}

MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;
    
    int len = strlen(g_LastCallResultString);
    int copyLen = min(len, bufferSize - 1);
    
    if (copyLen > 0) {
        memcpy(buffer, g_LastCallResultString, copyLen);
    }
    buffer[copyLen] = '\0';
    
    return copyLen;
}

MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        std::setlocale(LC_ALL, "C");
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        break;
    }
    return TRUE;
} 
.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为您的实际DLL地址

调试输出 ("=== 测试稳定版本RemoteCallDLL ===")
调试输出 ("特点：")
调试输出 ("✅ 绝对不会闪退 - 完整异常处理")
调试输出 ("✅ 智能参数解析 - 支持多种参数类型")
调试输出 ("✅ 详细调试信息 - 显示完整调用过程")
调试输出 ("✅ 多重调用尝试 - 自动尝试不同调用方式")
调试输出 ("")

' 测试您的具体问题
测试您的原始问题 ()

.子程序 测试您的原始问题

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试您的原始问题 ===")
调试输出 ("原始调用：远程调用DLL(地址, \"测试\", \"1\", 1, 文本结果, 3, 1024)")
调试输出 ("")

' 初始化
文本结果 = ""

' 您的原始调用
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 文本结果, 3, 1024)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 文本结果 + "]")
调试输出 ("文本结果长度: " + 到文本 (取文本长度 (文本结果)))

' 获取详细调试信息
获取调试信息 (调试信息, 2048)
调试输出 ("详细调试信息:")
调试输出 (调试信息)

调试输出 ("")

' 分析结果
如果 (调用成功 = 1)
    如果 (文本结果 = "假" 或者 文本结果 = "0")
        调试输出 ("✅ 调用成功！函数返回值确实是0/false")
        调试输出 ("这说明'测试'函数正常执行，返回值就是0")
        调试输出 ("这不是错误，而是函数的正常行为")
    否则如果 (取文本长度 (文本结果) > 0)
        调试输出 ("✅ 调用成功！获得文本结果：" + 文本结果)
    否则
        调试输出 ("⚠️ 调用成功但结果为空，可能需要尝试其他函数名")
    如果真结束
否则
    调试输出 ("❌ 调用失败，请检查DLL路径和函数名")
如果真结束

调试输出 ("")

' 尝试其他可能的函数名
测试其他函数名 ()

.子程序 测试其他函数名

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型
.局部变量 函数名列表, 文本型, , "6"
.局部变量 i, 整数型

调试输出 ("=== 测试其他可能的函数名 ===")

函数名列表 [1] = "文本测试"
函数名列表 [2] = "Text"
函数名列表 [3] = "GetText"
函数名列表 [4] = "Version"
函数名列表 [5] = "GetVersion"
函数名列表 [6] = "测试函数"

计次循环首 (6, i)
    调试输出 ("测试函数: " + 函数名列表 [i])

    ' 带参数测试
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, 函数名列表 [i], "1", 1, 文本结果, 3, 1024)
    调试输出 ("  带参数结果: [" + 文本结果 + "]")

    ' 无参数测试
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, 函数名列表 [i], "", 0, 文本结果, 3, 1024)
    调试输出 ("  无参数结果: [" + 文本结果 + "]")

    调试输出 ("")
计次循环尾 ()

调试输出 ("=== 测试不同返回类型 ===")

' 测试整数返回
.局部变量 整数结果, 整数型
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 整数结果, 1, 0)
调试输出 ("整数返回: " + 到文本 (整数结果))

' 测试浮点返回
.局部变量 浮点结果, 小数型
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 浮点结果, 2, 0)
调试输出 ("浮点返回: " + 到文本 (浮点结果))

' 测试自动类型
文本结果 = ""
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 文本结果, 5, 1024)
调试输出 ("自动类型返回: [" + 文本结果 + "]")

调试输出 ("")
调试输出 ("=== 测试完成 ===")
调试输出 ("总结：")
调试输出 ("1. 如果所有调用都返回'假'或'0'，说明函数确实返回0")
调试输出 ("2. 如果某些函数返回文本，说明找到了正确的文本函数")
调试输出 ("3. 如果调用失败，请检查DLL路径是否正确")
调试输出 ("4. 查看调试信息了解详细的调用过程")

.子程序 测试安全性

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型

调试输出 ("")
调试输出 ("=== 测试安全性（确保不会闪退） ===")

' 测试各种可能导致崩溃的情况
调试输出 ("测试1：空DLL路径")
调用成功 = 远程调用DLL ("", "函数", "", 0, 结果, 3, 1024)
调试输出 ("结果: " + 结果)

调试输出 ("测试2：不存在的DLL")
调用成功 = 远程调用DLL ("不存在.dll", "函数", "", 0, 结果, 3, 1024)
调试输出 ("结果: " + 结果)

调试输出 ("测试3：不存在的函数")
调用成功 = 远程调用DLL (地址, "不存在的函数", "", 0, 结果, 3, 1024)
调试输出 ("结果: " + 结果)

调试输出 ("测试4：超长参数")
调用成功 = 远程调用DLL (地址, "测试", "这是一个非常长的参数字符串用来测试参数解析的稳定性", 1, 结果, 3, 1024)
调试输出 ("结果: " + 结果)

调试输出 ("✅ 所有安全性测试完成，程序没有闪退！")

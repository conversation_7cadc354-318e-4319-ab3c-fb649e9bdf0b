#define MYDLL_EXPORTS

#include "mydll.h"
#include <windows.h>
#include <psapi.h>
#include <commctrl.h>
#include <tlhelp32.h>
#include <wininet.h>
#include <urlmon.h>
#include <shlwapi.h>

// 链接所需的库
#pragma comment(lib, "psapi.lib")
#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")
#pragma comment(lib, "shlwapi.lib")

// 全局变量
DWORD g_MainProcessId = 0;
BOOL g_ForceExit = FALSE;
DWORD g_MainThreadId = 0;
HWND g_MainWindow = NULL;

// 存储初始窗口状态
int g_InitialWindowCount = 0;
HWND g_MonitoredWindows[64] = {0};

// 全局变量用于存储上次调用的结果（向后兼容）
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0; // 0=int, 1=double, 2=string

// 前向声明
HWND FindMainWindow();
BOOL IsMainWindowAlive();
void ForceTerminateProcess();

// 窗口枚举回调函数
BOOL CALLBACK EnumWindowsProc(HWND hwnd, LPARAM lParam) {
    BOOL* windowFound = (BOOL*)lParam;
    
    if (IsWindow(hwnd) && IsWindowVisible(hwnd)) {
        DWORD pid = 0;
        GetWindowThreadProcessId(hwnd, &pid);
        
        if (pid == g_MainProcessId) {
            char className[64] = {0};
            GetClassNameA(hwnd, className, sizeof(className));
            
            // 过滤系统窗口
            if (strcmp(className, "Shell_TrayWnd") != 0 && strcmp(className, "Progman") != 0) {
                *windowFound = TRUE;
                return FALSE; // 停止枚举
            }
        }
    }
    
    return TRUE; // 继续枚举
}

// 初始化窗口监控
void InitWindowMonitoring() {
    // 记录当前进程的所有顶级窗口
    g_InitialWindowCount = 0;
    HWND hwnd = GetTopWindow(NULL);
    
    while (hwnd && g_InitialWindowCount < 64) {
        if (IsWindowVisible(hwnd)) {
            DWORD pid = 0;
            GetWindowThreadProcessId(hwnd, &pid);
            
            if (pid == g_MainProcessId) {
                g_MonitoredWindows[g_InitialWindowCount++] = hwnd;
            }
        }
        hwnd = GetWindow(hwnd, GW_HWNDNEXT);
    }
}

// 查找主窗口函数 - 增强版
HWND FindMainWindow()
{
    // 如果已有有效的主窗口，检查它是否仍然有效
    if (g_MainWindow && IsWindow(g_MainWindow) && IsWindowVisible(g_MainWindow)) {
        return g_MainWindow;
    }
    
    // 主窗口无效，需要重新查找
    g_MainWindow = NULL;
    
    DWORD pid = GetCurrentProcessId();
    HWND hwnd = GetTopWindow(NULL);
    HWND result = NULL;
    HWND bestWindow = NULL;
    DWORD bestArea = 0;
    
    // 限制搜索数量
    int searchCount = 0;
    
    while (hwnd && searchCount < 50) {
        if (IsWindowVisible(hwnd)) {
            DWORD winPid = 0;
            DWORD threadId = GetWindowThreadProcessId(hwnd, &winPid);
            
            if (winPid == pid) {
                char className[64] = {0};
                GetClassNameA(hwnd, className, sizeof(className));
                
                // 过滤系统窗口
                if (strcmp(className, "Shell_TrayWnd") != 0 && 
                    strcmp(className, "Progman") != 0) {
                    
                    // 优先返回主线程创建的窗口
                    if (threadId == g_MainThreadId) {
                        g_MainWindow = hwnd;
                        return hwnd;
                    }
                    
                    // 计算窗口面积作为重要性指标
                    RECT rect;
                    if (GetWindowRect(hwnd, &rect)) {
                        DWORD area = (rect.right - rect.left) * (rect.bottom - rect.top);
                        if (area > bestArea) {
                            bestArea = area;
                            bestWindow = hwnd;
                        }
                    }
                    
                    if (!result) {
                        result = hwnd;
                    }
                }
            }
        }
        
        searchCount++;
        hwnd = GetWindow(hwnd, GW_HWNDNEXT);
    }
    
    // 优先使用面积最大的窗口
    if (bestWindow) {
        g_MainWindow = bestWindow;
        return bestWindow;
    }
    
    if (result) {
        g_MainWindow = result;
    }
    
    return result;
}

// 检查主窗口是否存在 - 增强版
BOOL IsMainWindowAlive()
{
    // 检查主窗口是否仍然有效
    if (g_MainWindow && IsWindow(g_MainWindow) && IsWindowVisible(g_MainWindow)) {
        return TRUE;
    }
    
    // 尝试重新查找主窗口
    if (FindMainWindow() != NULL) {
        return TRUE;
    }
    
    // 检查是否有任何属于我们进程的可见窗口
    BOOL hasVisibleWindow = FALSE;
    EnumWindows(EnumWindowsProc, (LPARAM)&hasVisibleWindow);
    if (hasVisibleWindow) {
        return TRUE;
    }
    
    // 检查初始记录的窗口是否还存在
    if (g_InitialWindowCount > 0) {
        for (int i = 0; i < g_InitialWindowCount; i++) {
            if (IsWindow(g_MonitoredWindows[i]) && IsWindowVisible(g_MonitoredWindows[i])) {
                return TRUE;
            }
        }
    }
    
    // 如果初始有窗口但现在全都不存在了，说明窗口已关闭
    return (g_InitialWindowCount == 0);
}

// 终止进程 - 增强版
void ForceTerminateProcess()
{
    // 设置退出标志
    g_ForceExit = TRUE;
    
    // 确保所有线程都有机会看到退出标志
    Sleep(10);
    
    // 强制清理内存
    HANDLE hProcess = GetCurrentProcess();
    EmptyWorkingSet(hProcess);
    SetProcessWorkingSetSize(hProcess, (SIZE_T)-1, (SIZE_T)-1);
    
    // 终止进程
    ExitProcess(0);
}

/**
 * 程序延时函数 - 超级流畅优化版
 * 
 * @param delayTime 延时毫秒数
 * @return 成功返回1，失败返回0
 */
MYDLL_API int __stdcall ProgramDelay(int delayTime)
{
    // 参数检查
    if (delayTime <= 0) {
        Sleep(1);
        return 1;
    }
    
    // 检查退出标志
    if (g_ForceExit) {
        return 0;
    }
    
    // 确保窗口监控系统已初始化
    static BOOL monitoringInitialized = FALSE;
    if (!monitoringInitialized) {
        InitWindowMonitoring();
        monitoringInitialized = TRUE;
    }
    
    // 降低线程优先级以减少CPU占用
    HANDLE hThread = GetCurrentThread();
    int oldPriority = GetThreadPriority(hThread);
    SetThreadPriority(hThread, THREAD_PRIORITY_BELOW_NORMAL);
    
    // 内存优化 - 仅对较长延时执行
    if (delayTime > 500) {
        HANDLE hProcess = GetCurrentProcess();
        SetProcessWorkingSetSize(hProcess, (SIZE_T)-1, (SIZE_T)-1);
        EmptyWorkingSet(hProcess);
    }
    
    // 高精度计时初始化
    LARGE_INTEGER frequency, startCount, currentCount;
    QueryPerformanceFrequency(&frequency);
    QueryPerformanceCounter(&startCount);
    
    // 计算结束时间点
    LONGLONG endCount = startCount.QuadPart + (delayTime * frequency.QuadPart / 1000);
    
    // 主循环
    BOOL windowFound = IsMainWindowAlive();
    HWND hMainWnd = g_MainWindow;
    
    // 上次窗口检查时间
    DWORD lastCheckTime = GetTickCount();
    
    // 轻量级轮询检测
    while (1) {
        // 定期进行更全面的窗口状态检查（更频繁地检查）
        DWORD currentTime = GetTickCount();
        if (currentTime - lastCheckTime >= 20) { // 每20ms检查一次（原来是更长时间）
            lastCheckTime = currentTime;
            
            // 全面检查窗口状态
            if (!IsMainWindowAlive()) {
                // 如果之前发现过窗口，现在却找不到了，说明窗口已关闭
                if (windowFound) {
                    ForceTerminateProcess();
                    return 0;
                }
            }
            
            // 更新主窗口句柄
            hMainWnd = g_MainWindow;
        }
        
        // 快速检查当前窗口句柄
        if (hMainWnd) {
            if (!IsWindow(hMainWnd) || !IsWindowVisible(hMainWnd)) {
                if (windowFound) {
                    ForceTerminateProcess();
                    return 0;
                }
            }
        }
        
        // 处理消息队列保持UI响应
        MSG msg;
        while (PeekMessage(&msg, NULL, 0, 0, PM_REMOVE)) {
            if (msg.message == WM_QUIT || msg.message == WM_CLOSE || msg.message == WM_DESTROY) {
                ForceTerminateProcess();
                return 0;
            }
            TranslateMessage(&msg);
            DispatchMessage(&msg);
        }
        
        // 检查延时是否结束
        QueryPerformanceCounter(&currentCount);
        if (currentCount.QuadPart >= endCount) {
            break;
        }
        
        // 计算剩余时间
        LONGLONG remainingCount = endCount - currentCount.QuadPart;
        DWORD remainingMs = (DWORD)(remainingCount * 1000 / frequency.QuadPart);
        
        // 自适应休眠 - 提高流畅度
        if (remainingMs > 100) {
            // 较长时间 - 使用较长休眠
            Sleep(10);
        } else if (remainingMs > 20) {
            // 中等时间 - 短休眠
            Sleep(5);
        } else if (remainingMs > 5) {
            // 短时间 - 最小休眠
            Sleep(1);
        } else {
            // 几乎完成 - 轻微让步
            Sleep(0);
        }
    }
    
    // 恢复线程优先级
    SetThreadPriority(hThread, oldPriority);
    
    // 最终检查一次窗口状态
    if (!IsMainWindowAlive() && windowFound) {
        ForceTerminateProcess();
        return 0;
    }
    
    // 检查一次全局退出标志
    if (g_ForceExit) {
        return 0;
    }
    
    return 1;
}

/**
 * 保存控件配置的函数
 * 
 * @param configFile 配置文件路径
 * @param parentWindow 父窗口句柄
 * @return 成功保存的控件数量
 */
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow)
{
    // 简单实现：返回1表示成功（实际项目中可以实现真正的配置保存逻辑）
    return 1;
}

/**
 * 读取控件配置的函数
 * 
 * @param configFile 配置文件路径
 * @param parentWindow 父窗口句柄
 * @return 成功读取的控件数量
 */
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow)
{
    // 简单的存根实现
    return 0;
}

/**
 * 远程调用DLL函数 - 从远程路径下载并调用DLL函数
 * 
 * @param remotePath 远程DLL文件路径（支持HTTP/HTTPS网盘链接和本地路径）
 * @param functionName 要调用的函数名称
 * @param parameters 传递给函数的参数（以字符串形式）
 * @param paramCount 参数个数
 * @return 成功返回1，失败返回0
 */
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName, const char* parameters, int paramCount)
{
    // 参数检查
    if (!remotePath || !functionName) {
        return 0;
    }
    
    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;
    
    // 检查是否是本地路径
    if (strstr(remotePath, "http://") == remotePath || strstr(remotePath, "https://") == remotePath) {
        // 远程URL - 需要下载
        char tempPath[MAX_PATH];
        GetTempPathA(MAX_PATH, tempPath);
        GetTempFileNameA(tempPath, "RDLL", 0, dllPath);
        strcat_s(dllPath, sizeof(dllPath), ".dll");
        
        try {
            HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
            if (FAILED(hr)) {
                return 0;
            }
            needCleanup = TRUE;
        } catch (...) {
            return 0;
        }
    } else {
        // 本地路径 - 直接使用
        strcpy_s(dllPath, sizeof(dllPath), remotePath);
    }
    
    // 检查文件是否存在
    if (GetFileAttributesA(dllPath) == INVALID_FILE_ATTRIBUTES) {
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // 加载DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // 获取函数地址
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }
    
    // 根据参数个数调用函数
    int result = 0;
    try {
        switch (paramCount) {
        case 0:
            {
                typedef int (__stdcall *Func0)();
                Func0 func = (Func0)procAddr;
                result = func();
            }
            break;
            
        case 1:
            {
                typedef int (__stdcall *Func1)(const char*);
                Func1 func = (Func1)procAddr;
                result = func(parameters);
            }
            break;
            
        case 2:
            {
                // SaveConfig函数签名: SaveConfig(const char* configFile, long parentWindow)
                char paramsCopy[1024];
                strcpy_s(paramsCopy, sizeof(paramsCopy), parameters ? parameters : "");
                
                char* param1 = paramsCopy;
                char* param2Str = strchr(param1, ',');
                long param2 = 0;
                
                if (param2Str) {
                    *param2Str = '\0';
                    param2Str++;
                    param2 = atol(param2Str);  // 使用atol转换为long
                }
                
                typedef int (__stdcall *Func2)(const char*, long);
                Func2 func = (Func2)procAddr;
                result = func(param1, param2);
            }
            break;
            
        case 3:
            {
                char paramsCopy[1024];
                strcpy_s(paramsCopy, sizeof(paramsCopy), parameters ? parameters : "");
                
                char* param1 = paramsCopy;
                char* param2Str = strchr(param1, ',');
                char* param3Str = NULL;
                long param2 = 0, param3 = 0;
                
                if (param2Str) {
                    *param2Str = '\0';
                    param2Str++;
                    param2 = atol(param2Str);
                    
                    param3Str = strchr(param2Str, ',');
                    if (param3Str) {
                        param3Str++;
                        param3 = atol(param3Str);
                    }
                }
                
                typedef int (__stdcall *Func3)(const char*, long, long);
                Func3 func = (Func3)procAddr;
                result = func(param1, param2, param3);
            }
            break;
            
        default:
            result = 0;
            break;
        }
    } catch (...) {
        result = 0;
    }
    
    // 清理资源
    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);
    
    return (result != 0) ? 1 : 0;
}

/**
 * 获取上次调用的整数结果
 * @return 上次调用的整数结果
 */
MYDLL_API int __stdcall GetLastCallResultInt()
{
    return g_LastCallResult;
}

/**
 * 获取上次调用的浮点数结果
 * @return 上次调用的浮点数结果
 */
MYDLL_API double __stdcall GetLastCallResultDouble()
{
    return g_LastCallResultDouble;
}

/**
 * 获取上次调用的字符串结果
 * @param buffer 接收字符串结果的缓冲区
 * @param bufferSize 缓冲区大小
 * @return 复制到缓冲区的字符数量
 */
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize)
{
    if (!buffer || bufferSize <= 0) {
        return 0;
    }
    
    // 安全复制字符串
    int len = strlen(g_LastCallResultString);
    int copyLen = min(len, bufferSize - 1);
    
    if (copyLen > 0) {
        memcpy(buffer, g_LastCallResultString, copyLen);
    }
    buffer[copyLen] = '\0';
    
    return copyLen;
}

/**
 * 获取上次调用的结果类型
 * @return 结果类型：0=整数, 1=浮点数, 2=字符串, 3=布尔值
 */
MYDLL_API int __stdcall GetLastCallResultType()
{
    return g_LastCallResultType;
}

/**
 * 检查上次调用是否有有效结果
 * @return 如果有结果返回TRUE，否则返回FALSE
 */
MYDLL_API int __stdcall HasLastCallResult()
{
    return g_LastCallHasResult ? 1 : 0;
}

// DLL入口函数
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved)
{
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        // 初始化
        g_MainProcessId = GetCurrentProcessId();
        g_MainThreadId = GetCurrentThreadId();
        g_ForceExit = FALSE;
        g_MainWindow = NULL;
        // 初始化窗口监控
        InitWindowMonitoring();
        break;
        
    case DLL_PROCESS_DETACH:
        // 清理
        g_ForceExit = TRUE;
        break;
    }
    
    return TRUE;
} 
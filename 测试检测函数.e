.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为您的实际DLL地址

调试输出 ("=== 测试正确的函数名：检测 ===")
调试输出 ("发现问题：您调用的是'测试'，但DLL中的函数是'检测'")
调试输出 ("")

' 测试正确的函数名
测试检测函数 ()

.子程序 测试检测函数

.局部变量 文本结果, 文本型
.局部变量 整数结果, 整数型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 使用正确的函数名'检测' ===")

' 测试1：文本返回类型
调试输出 ("测试1：检测函数 - 文本返回")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "1", 1, 文本结果, 3, 1024)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 文本结果 + "]")
调试输出 ("文本结果长度: " + 到文本 (取文本长度 (文本结果)))

获取调试信息 (调试信息, 2048)
调试输出 ("调试信息: " + 调试信息)
调试输出 ("")

' 测试2：整数返回类型
调试输出 ("测试2：检测函数 - 整数返回")
调用成功 = 远程调用DLL (地址, "检测", "1", 1, 整数结果, 1, 0)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("整数结果: " + 到文本 (整数结果))

获取调试信息 (调试信息, 2048)
调试输出 ("调试信息: " + 调试信息)
调试输出 ("")

' 测试3：无参数调用
调试输出 ("测试3：检测函数 - 无参数")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "", 0, 文本结果, 3, 1024)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 文本结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("调试信息: " + 调试信息)
调试输出 ("")

' 测试4：不同参数值
调试输出 ("测试4：检测函数 - 不同参数值")
.局部变量 参数列表, 文本型, , "5"
.局部变量 i, 整数型

参数列表 [1] = "0"
参数列表 [2] = "1"
参数列表 [3] = "2"
参数列表 [4] = "测试"
参数列表 [5] = "hello"

计次循环首 (5, i)
    调试输出 ("  测试参数: " + 参数列表 [i])
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, "检测", 参数列表 [i], 1, 文本结果, 3, 1024)
    调试输出 ("    结果: [" + 文本结果 + "]")
    
    ' 也测试整数返回
    调用成功 = 远程调用DLL (地址, "检测", 参数列表 [i], 1, 整数结果, 1, 0)
    调试输出 ("    整数: " + 到文本 (整数结果))
    调试输出 ("")
计次循环尾 ()

' 测试5：自动类型
调试输出 ("测试5：检测函数 - 自动类型")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "1", 1, 文本结果, 5, 1024)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("自动类型结果: [" + 文本结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("调试信息: " + 调试信息)
调试输出 ("")

调试输出 ("=== 测试其他可能的函数 ===")

' 测试其他可能的函数名
.局部变量 函数名列表, 文本型, , "8"
函数名列表 [1] = "检测"      ' 已知存在
函数名列表 [2] = "测试"      ' 您之前尝试的
函数名列表 [3] = "返回"      ' 可能的函数名
函数名列表 [4] = "文本"      ' 可能的函数名
函数名列表 [5] = "获取"      ' 可能的函数名
函数名列表 [6] = "版本"      ' 可能的函数名
函数名列表 [7] = "信息"      ' 可能的函数名
函数名列表 [8] = "状态"      ' 可能的函数名

计次循环首 (8, i)
    调试输出 ("测试函数: " + 函数名列表 [i])
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, 函数名列表 [i], "1", 1, 文本结果, 3, 1024)
    
    如果 (调用成功 = 1)
        调试输出 ("  ✅ 成功: [" + 文本结果 + "]")
    否则
        调试输出 ("  ❌ 失败: [" + 文本结果 + "]")
    如果真结束
    
    调试输出 ("")
计次循环尾 ()

调试输出 ("=== 总结 ===")
调试输出 ("1. 正确的函数名是'检测'，不是'测试'")
调试输出 ("2. 请查看上面的测试结果，找到返回正确内容的调用方式")
调试输出 ("3. 如果'检测'函数返回数字，可能需要其他函数获取文本")
调试输出 ("4. 查看调试信息了解详细的调用过程")

.子程序 对比测试

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型

调试输出 ("")
调试输出 ("=== 对比测试：错误 vs 正确 ===")

' 错误的函数名（您之前的调用）
调试输出 ("错误调用：远程调用DLL(地址, \"测试\", \"1\", 1, 文本结果, 3, 1024)")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 文本结果, 3, 1024)
调试输出 ("结果: [" + 文本结果 + "]")
调试输出 ("")

' 正确的函数名
调试输出 ("正确调用：远程调用DLL(地址, \"检测\", \"1\", 1, 文本结果, 3, 1024)")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "1", 1, 文本结果, 3, 1024)
调试输出 ("结果: [" + 文本结果 + "]")
调试输出 ("")

调试输出 ("现在您应该能看到区别了！")

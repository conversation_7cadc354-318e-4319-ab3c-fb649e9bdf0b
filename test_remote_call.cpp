#include <windows.h>
#include <iostream>
#include <string>

// 函数声明
typedef int (__stdcall *RemoteCallDLLFunc)(const char*, const char*, const char*, int, void*, int, int);
typedef int (__stdcall *GetDebugInfoFunc)(char*, int);
typedef int (__stdcall *GetLastCallResultStringFunc)(char*, int);

int main() {
    // 设置控制台编码
    SetConsoleOutputCP(CP_UTF8);
    
    // 加载我们的DLL
    HMODULE hMyDll = LoadLibraryA("MyDll.dll");
    if (!hMyDll) {
        std::cout << "❌ 无法加载 MyDll.dll" << std::endl;
        return 1;
    }
    
    // 获取函数地址
    RemoteCallDLLFunc RemoteCallDLL = (RemoteCallDLLFunc)GetProcAddress(hMyDll, "RemoteCallDLL");
    GetDebugInfoFunc GetDebugInfo = (GetDebugInfoFunc)GetProcAddress(hMyDll, "GetDebugInfo");
    GetLastCallResultStringFunc GetLastCallResultString = (GetLastCallResultStringFunc)GetProcAddress(hMyDll, "GetLastCallResultString");
    
    if (!RemoteCallDLL || !GetDebugInfo || !GetLastCallResultString) {
        std::cout << "❌ 无法获取函数地址" << std::endl;
        FreeLibrary(hMyDll);
        return 1;
    }
    
    std::cout << "🎯 开始测试 RemoteCallDLL 函数..." << std::endl;
    std::cout << "================================" << std::endl;
    
    // 创建测试DLL
    std::cout << "📝 创建测试DLL..." << std::endl;
    system("echo const char* __stdcall 文本测试() { return \"测试文本结果\"; } > temp_test.cpp");
    system("cl /LD /Fe:TestDll.dll temp_test.cpp >nul 2>&1");
    
    // 测试1: 整数返回类型 (应该正常工作)
    std::cout << "\n🧪 测试1: 整数返回类型" << std::endl;
    int intResult = 0;
    int success = RemoteCallDLL("TestDll.dll", "文本测试", "", 0, &intResult, 1, 0);
    
    char debugInfo[1024];
    GetDebugInfo(debugInfo, sizeof(debugInfo));
    std::cout << "调试信息: " << debugInfo << std::endl;
    std::cout << "调用结果: " << (success ? "成功" : "失败") << std::endl;
    std::cout << "整数结果: " << intResult << std::endl;
    
    // 测试2: 字符串返回类型 (这是关键测试!)
    std::cout << "\n🎯 测试2: 字符串返回类型 (resultType=3)" << std::endl;
    char textResult[4096] = {0};
    success = RemoteCallDLL("TestDll.dll", "文本测试", "", 0, textResult, 3, 4096);
    
    GetDebugInfo(debugInfo, sizeof(debugInfo));
    std::cout << "调试信息: " << debugInfo << std::endl;
    std::cout << "调用结果: " << (success ? "成功" : "失败") << std::endl;
    std::cout << "文本结果: [" << textResult << "]" << std::endl;
    std::cout << "文本长度: " << strlen(textResult) << std::endl;
    
    // 测试3: 使用GetLastCallResultString获取结果
    std::cout << "\n🔍 测试3: 使用GetLastCallResultString获取结果" << std::endl;
    char lastResult[4096] = {0};
    int len = GetLastCallResultString(lastResult, sizeof(lastResult));
    std::cout << "获取的结果: [" << lastResult << "]" << std::endl;
    std::cout << "结果长度: " << len << std::endl;
    
    // 清理
    FreeLibrary(hMyDll);
    DeleteFileA("TestDll.dll");
    DeleteFileA("temp_test.cpp");
    DeleteFileA("TestDll.lib");
    DeleteFileA("TestDll.exp");
    DeleteFileA("temp_test.obj");
    
    std::cout << "\n✅ 测试完成!" << std::endl;
    std::cout << "如果测试2显示了文本结果，说明修复成功！" << std::endl;
    
    system("pause");
    return 0;
}

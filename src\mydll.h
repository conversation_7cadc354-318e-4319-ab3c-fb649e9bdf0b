#pragma once

// DLL export macro definitions
#ifdef MYDLL_EXPORTS
    #define MYDLL_API __declspec(dllexport)
#else
    #define MYDLL_API __declspec(dllimport)
#endif

// Use __stdcall calling convention for easy language compatibility
#ifdef __cplusplus
extern "C" {
#endif

/**
 * Save all control configurations in a window to INI file
 * 
 * @param configFile Configuration file path, can be empty for default
 * @param parentWindow Parent window handle, 0 for current active window
 * @return Number of controls successfully saved, 0 on failure
 */
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow);

/**
 * Load configuration from INI file to all controls in a window
 * 
 * @param configFile Configuration file path, can be empty for default
 * @param parentWindow Parent window handle, 0 for current active window
 * @return Number of controls successfully loaded, 0 on failure
 */
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow);

/**
 * Program delay function with window monitoring
 * 
 * @param delayTime Delay time in milliseconds
 * @return 1 on success, 0 on failure
 */
MYDLL_API int __stdcall ProgramDelay(int delayTime);

/**
 * Remote DLL call function - Simplified version with unified result parameter
 * 
 * Features:
 * - Download from remote URLs (HTTP/HTTPS) with caching mechanism
 * - Smart parameter type detection (string, int, long, double, bool, pointer)
 * - Support for 0-8 parameters
 * - Enhanced download engine with integrity verification
 * - Simplified result retrieval with single result parameter
 * - Support for various cloud storage links
 * 
 * @param remotePath Remote DLL file URL (HTTP/HTTPS) or local path
 * @param functionName Name of the function to call
 * @param parameters Parameters to pass to the function (comma-separated string)
 *                   Supports automatic type detection:
 *                   - Strings: "hello world"
 *                   - Integers: "123", "-456"
 *                   - Booleans: "true", "false", "1", "0"
 *                   - Doubles: "3.14", "-2.5"
 *                   - Pointers: "0x12345678"
 * @param paramCount Number of parameters (0-8)
 * @param result Pointer to receive result (type depends on resultType parameter)
 * @param resultType Type of result to retrieve:
 *                   0 = No result (pass NULL for result parameter)
 *                   1 = Integer result (result should point to int variable)
 *                   2 = Double result (result should point to double variable)  
 *                   3 = String result (result should point to char buffer)
 *                   4 = Boolean result (result should point to BOOL variable)
 *                   5 = Smart mode (auto-detect best format, result should point to char buffer)
 * @param bufferSize Size of string buffer (only used when resultType = 3 or 5)
 * @return 1 on success, 0 on failure
 * 
 * Example usage:
 *   // Get integer result
 *   int intResult;
 *   RemoteCallDLL("C:/test.dll", "Add", "1,2", 2, &intResult, 1, 0);
 *   
 *   // Get double result
 *   double doubleResult;
 *   RemoteCallDLL("C:/test.dll", "Divide", "10,3", 2, &doubleResult, 2, 0);
 *   
 *   // Get string result
 *   char strResult[256];
 *   RemoteCallDLL("C:/test.dll", "GetVersion", "", 0, strResult, 3, 256);
 *   
 *   // Get boolean result
 *   BOOL boolResult;
 *   RemoteCallDLL("C:/test.dll", "IsReady", "", 0, &boolResult, 4, 0);
 *   
 *   // Smart mode (auto-detect best format)
 *   char smartResult[256];
 *   RemoteCallDLL("C:/test.dll", "ComplexFunction", "data", 1, smartResult, 5, 256);
 *   
 *   // No result needed
 *   RemoteCallDLL("C:/test.dll", "Initialize", "", 0, NULL, 0, 0);
 */
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName, const char* parameters, int paramCount,
                                      void* result, int resultType, int bufferSize);

/**
 * Backward compatibility functions for getting last call results
 * These functions provide access to the results of the last RemoteCallDLL call
 */

/**
 * Get the integer result of the last RemoteCallDLL call
 * @return Last call result as integer
 */
MYDLL_API int __stdcall GetLastCallResultInt();

/**
 * Get the double result of the last RemoteCallDLL call
 * @return Last call result as double
 */
MYDLL_API double __stdcall GetLastCallResultDouble();

/**
 * Get the string result of the last RemoteCallDLL call
 * @param buffer Buffer to receive the string result
 * @param bufferSize Size of the buffer
 * @return Number of characters copied to buffer
 */
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize);

/**
 * Get the result type of the last RemoteCallDLL call
 * @return Result type: 0=integer, 1=double, 2=string, 3=boolean
 */
MYDLL_API int __stdcall GetLastCallResultType();

/**
 * Check if the last RemoteCallDLL call has a valid result
 * @return TRUE if has result, FALSE otherwise
 */
MYDLL_API int __stdcall HasLastCallResult();

#ifdef __cplusplus
}
#endif 
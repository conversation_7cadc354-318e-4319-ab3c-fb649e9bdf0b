.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为您的实际DLL地址

调试输出 ("=== 测试智能参数处理 ===")
调试输出 ("新功能：")
调试输出 ("✅ 智能参数类型识别 - 自动识别整数/字符串/浮点数")
调试输出 ("✅ 多重调用尝试 - 自动尝试4种不同的参数类型组合")
调试输出 ("✅ 详细参数信息 - 显示每个参数的识别类型")
调试输出 ("✅ 调用过程追踪 - 显示哪种尝试成功")
调试输出 ("")

' 测试您的例子
测试您的例子 ()

' 测试各种参数组合
测试各种参数组合 ()

.子程序 测试您的例子

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试您的例子：99,98,你好 ===")

' 您的例子：远程调用DLL(地址, "检测", "99,98,你好", 3, 文本结果, 3, 1024)
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "99,98,你好", 3, 文本结果, 3, 1024)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 文本结果 + "]")

' 获取详细调试信息
获取调试信息 (调试信息, 2048)
调试输出 ("详细调试信息:")
调试输出 (调试信息)

调试输出 ("")
调试输出 ("参数解析分析:")
调试输出 ("第1个参数：99 → 应该识别为 int")
调试输出 ("第2个参数：98 → 应该识别为 int")
调试输出 ("第3个参数：你好 → 应该识别为 str")
调试输出 ("")

.子程序 测试各种参数组合

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型
.局部变量 测试用例, 文本型, , "10"
.局部变量 i, 整数型

调试输出 ("=== 测试各种参数类型组合 ===")

' 准备测试用例
测试用例 [1] = "123"                    ' 单个整数
测试用例 [2] = "hello"                  ' 单个字符串
测试用例 [3] = "3.14"                   ' 单个浮点数
测试用例 [4] = "123,456"                ' 两个整数
测试用例 [5] = "hello,world"            ' 两个字符串
测试用例 [6] = "123,hello"              ' 整数+字符串
测试用例 [7] = "hello,123"              ' 字符串+整数
测试用例 [8] = "3.14,2.71"              ' 两个浮点数
测试用例 [9] = "1,hello,3.14"           ' 混合三个参数
测试用例 [10] = "测试,123,你好,3.14"      ' 中文+混合类型

计次循环首 (10, i)
    调试输出 ("测试用例 " + 到文本 (i) + ": " + 测试用例 [i])

    ' 计算参数个数
    .局部变量 参数个数, 整数型
    参数个数 = 1
    .局部变量 j, 整数型
    计次循环首 (取文本长度 (测试用例 [i]), j)
        如果 (取文本中间 (测试用例 [i], j, 1) = ",")
            参数个数 = 参数个数 + 1
        如果真结束
    计次循环尾 ()

    ' 调用函数
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, "检测", 测试用例 [i], 参数个数, 文本结果, 3, 1024)

    调试输出 ("  参数个数: " + 到文本 (参数个数))
    调试输出 ("  调用成功: " + 到文本 (调用成功))
    调试输出 ("  结果: [" + 文本结果 + "]")

    ' 获取调试信息
    获取调试信息 (调试信息, 2048)
    调试输出 ("  调试: " + 调试信息)
    调试输出 ("")
计次循环尾 ()

调试输出 ("=== 测试特殊情况 ===")

' 测试空格处理
调试输出 ("测试空格处理:")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", " 123 , hello , 3.14 ", 3, 文本结果, 3, 1024)
调试输出 ("参数: \" 123 , hello , 3.14 \"")
调试输出 ("结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 测试长数字
调试输出 ("测试长数字:")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "1234567890", 1, 文本结果, 3, 1024)
调试输出 ("参数: \"1234567890\"")
调试输出 ("结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 测试中文参数
调试输出 ("测试中文参数:")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "你好,世界,测试", 3, 文本结果, 3, 1024)
调试输出 ("参数: \"你好,世界,测试\"")
调试输出 ("结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

调试输出 ("=== 测试完成 ===")
调试输出 ("总结：")
调试输出 ("1. 查看调试信息中的 ParamTypes 了解参数类型识别结果")
调试输出 ("2. 查看 CallAttempts 了解哪种调用方式成功")
调试输出 ("3. 系统会自动尝试4种不同的参数类型组合")
调试输出 ("4. 即使第一次尝试失败，后续尝试可能会成功")

.子程序 分析调试信息

调试输出 ("")
调试输出 ("=== 调试信息解读 ===")
调试输出 ("ParamTypes 说明：")
调试输出 ("  [0]=int  → 第1个参数识别为整数")
调试输出 ("  [1]=str  → 第2个参数识别为字符串")
调试输出 ("  [2]=float → 第3个参数识别为浮点数")
调试输出 ("")
调试输出 ("CallAttempts 说明：")
调试输出 ("  Attempt1(detected-types):OK → 使用识别的类型成功")
调试输出 ("  Attempt2(all-int):OK → 全部转为整数成功")
调试输出 ("  Attempt3(all-str):OK → 全部转为字符串成功")
调试输出 ("  Attempt4(raw-str):OK → 原始字符串作为单参数成功")
调试输出 ("")
调试输出 ("这样您就能知道API函数期望什么类型的参数了！")

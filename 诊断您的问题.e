.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.DLL命令 获取上次调用整数结果, 整数型, "MyDll.dll", "GetLastCallResultInt", 公开

.DLL命令 获取上次调用字符串结果, 整数型, "MyDll.dll", "GetLastCallResultString", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为您的实际DLL地址

调试输出 ("=== 诊断您的具体问题 ===")
调试输出 ("问题：调用返回'假'而不是期望的文本")
调试输出 ("原始调用：远程调用DLL(地址, \"测试\", \"1\", 1, 文本结果, 3, 1024)")
调试输出 ("")

' 执行详细诊断
执行详细诊断 ()

.子程序 执行详细诊断

.局部变量 文本结果, 文本型
.局部变量 整数结果, 整数型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型
.局部变量 字符串结果, 文本型

调试输出 ("=== 步骤1：完全复现您的问题 ===")

' 完全按照您的方式调用
文本结果 = ""
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 文本结果, 3, 1024)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 文本结果 + "]")
调试输出 ("文本结果长度: " + 到文本 (取文本长度 (文本结果)))

' 获取详细调试信息
获取调试信息 (调试信息, 2048)
调试输出 ("详细调试信息:")
调试输出 (调试信息)

调试输出 ("")

调试输出 ("=== 步骤2：获取原始返回值 ===")

' 获取原始整数返回值
整数结果 = 获取上次调用整数结果 ()
调试输出 ("原始整数返回值: " + 到文本 (整数结果) + " (0x" + 到十六进制 (整数结果) + ")")

' 尝试获取字符串结果
获取上次调用字符串结果 (字符串结果, 1024)
调试输出 ("从全局变量获取的字符串: [" + 字符串结果 + "]")

调试输出 ("")

调试输出 ("=== 步骤3：尝试不同的调用方式 ===")

' 尝试1：作为整数返回
调试输出 ("尝试1：作为整数返回")
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 整数结果, 1, 0)
调试输出 ("整数结果: " + 到文本 (整数结果))
获取调试信息 (调试信息, 2048)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 尝试2：无参数调用
调试输出 ("尝试2：无参数调用")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "测试", "", 0, 文本结果, 3, 1024)
调试输出 ("文本结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 尝试3：参数作为字符串传递
调试输出 ("尝试3：参数作为字符串传递")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 文本结果, 3, 1024)
调试输出 ("文本结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 尝试4：自动类型
调试输出 ("尝试4：自动类型")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "测试", "1", 1, 文本结果, 5, 1024)
调试输出 ("文本结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

调试输出 ("=== 步骤4：测试其他函数 ===")

' 测试其他可能的函数名
.局部变量 函数名列表, 文本型, , "5"
函数名列表 [1] = "测试"
函数名列表 [2] = "文本测试"
函数名列表 [3] = "Test"
函数名列表 [4] = "GetText"
函数名列表 [5] = "Version"

.局部变量 i, 整数型
计次循环首 (5, i)
    调试输出 ("测试函数: " + 函数名列表 [i])
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, 函数名列表 [i], "1", 1, 文本结果, 3, 1024)
    调试输出 ("  结果: [" + 文本结果 + "]")

    ' 也尝试无参数
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, 函数名列表 [i], "", 0, 文本结果, 3, 1024)
    调试输出 ("  无参数结果: [" + 文本结果 + "]")
    调试输出 ("")
计次循环尾 ()

调试输出 ("=== 诊断完成 ===")
调试输出 ("请查看上面的详细信息来分析问题原因")

.子程序 分析结果

调试输出 ("")
调试输出 ("=== 结果分析 ===")
调试输出 ("可能的原因：")
调试输出 ("1. 目标函数确实返回0/false - 这是正常的")
调试输出 ("2. 函数名不正确 - 尝试其他函数名")
调试输出 ("3. 参数类型不匹配 - 函数期望不同的参数类型")
调试输出 ("4. 调用约定不匹配 - 函数可能不是__stdcall")
调试输出 ("5. DLL路径错误 - 确认DLL文件存在且可访问")
调试输出 ("")
调试输出 ("解决建议：")
调试输出 ("1. 检查目标DLL的文档，确认函数名和参数")
调试输出 ("2. 使用工具如Dependency Walker查看DLL导出函数")
调试输出 ("3. 尝试不同的参数类型和调用方式")
调试输出 ("4. 查看详细调试信息中的调用尝试结果")

.子程序 测试DLL基本信息

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型

调试输出 ("")
调试输出 ("=== 测试DLL基本信息 ===")

' 测试DLL是否能正常加载
调用成功 = 远程调用DLL (地址, "不存在的函数", "", 0, 结果, 3, 1024)
调试输出 ("DLL加载测试结果: [" + 结果 + "]")

' 如果结果包含"Function not found"说明DLL加载成功但函数不存在
' 如果结果包含"Load failed"说明DLL加载失败

如果 (寻找文本 (结果, "Function not found", , 假) > 0)
    调试输出 ("✅ DLL加载成功，但函数'不存在的函数'确实不存在")
否则如果 (寻找文本 (结果, "Load failed", , 假) > 0)
    调试输出 ("❌ DLL加载失败，请检查DLL路径")
否则
    调试输出 ("⚠️ 未知状态，请检查DLL")
如果真结束

@echo off
chcp 65001 >nul
echo 🎯 编译并测试 RemoteCallDLL 修复版本
echo ========================================

call "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Auxiliary\Build\vcvars32.bat" >nul 2>&1

echo 📝 编译测试程序...
cl test_remote_call.cpp /Fe:test_remote_call.exe /O2 /MT /EHsc >nul 2>&1

if errorlevel 1 (
    echo ❌ 测试程序编译失败
    pause
    exit /b 1
)

echo ✅ 测试程序编译成功
echo 🚀 开始运行测试...
echo.

test_remote_call.exe

echo.
echo 🧹 清理临时文件...
del test_remote_call.obj >nul 2>&1

pause

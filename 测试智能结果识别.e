.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为您的实际DLL地址

调试输出 ("=== 智能结果类型识别测试 ===")
调试输出 ("🎯 新功能：")
调试输出 ("✅ 智能参数类型识别 - 14种数据类型自动识别")
调试输出 ("✅ 智能结果类型识别 - 自动判断最佳返回类型")
调试输出 ("✅ 自动类型选择 - resultType=5时自动选择最佳类型")
调试输出 ("✅ 函数名模式识别 - 根据函数名预测返回类型")
调试输出 ("✅ 返回值分析 - 分析实际返回值确定类型")
调试输出 ("")

' 测试智能结果识别
测试智能结果识别 ()

' 测试手动vs自动对比
测试手动vs自动对比 ()

.子程序 测试智能结果识别

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试智能结果类型识别 ===")

' 测试1：使用自动识别 (resultType=5)
调试输出 ("1. 自动识别测试 - resultType=5")
结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "99,98,你好", 3, 结果, 5, 1024)

调试输出 ("  调用成功: " + 到文本 (调用成功))
调试输出 ("  结果: [" + 结果 + "]")

获取调试信息 (调试信息, 2048)
调试输出 ("  调试信息: " + 调试信息)

' 从调试信息中提取检测到的类型
.局部变量 检测类型位置, 整数型
检测类型位置 = 寻找文本 (调试信息, "DetectedResultType:", , 假)
如果 (检测类型位置 > 0)
    .局部变量 检测类型, 文本型
    检测类型 = 取文本中间 (调试信息, 检测类型位置 + 19, 10)
    检测类型 = 取文本左边 (检测类型, 寻找文本 (检测类型, ",", , 假) - 1)
    调试输出 ("  🎯 系统检测到的结果类型: " + 检测类型)
如果真结束

调试输出 ("")

.子程序 测试手动vs自动对比

.局部变量 整数结果, 整数型
.局部变量 文本结果, 文本型
.局部变量 浮点结果, 小数型
.局部变量 逻辑结果, 逻辑型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 手动指定 vs 自动识别对比 ===")

' 测试同一个函数调用，使用不同的结果类型
调试输出 ("测试函数: 检测(\"99,98,你好\")")
调试输出 ("")

' 手动指定为整数
调试输出 ("1. 手动指定 - 整数型 (resultType=1)")
调用成功 = 远程调用DLL (地址, "检测", "99,98,你好", 3, 整数结果, 1, 0)
调试输出 ("  整数结果: " + 到文本 (整数结果))
获取调试信息 (调试信息, 2048)
调试输出 ("  检测类型: " + 提取检测类型 (调试信息))
调试输出 ("")

' 手动指定为文本
调试输出 ("2. 手动指定 - 文本型 (resultType=3)")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "99,98,你好", 3, 文本结果, 3, 1024)
调试输出 ("  文本结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("  检测类型: " + 提取检测类型 (调试信息))
调试输出 ("")

' 手动指定为浮点
调试输出 ("3. 手动指定 - 浮点型 (resultType=2)")
调用成功 = 远程调用DLL (地址, "检测", "99,98,你好", 3, 浮点结果, 2, 0)
调试输出 ("  浮点结果: " + 到文本 (浮点结果))
获取调试信息 (调试信息, 2048)
调试输出 ("  检测类型: " + 提取检测类型 (调试信息))
调试输出 ("")

' 手动指定为逻辑
调试输出 ("4. 手动指定 - 逻辑型 (resultType=4)")
调用成功 = 远程调用DLL (地址, "检测", "99,98,你好", 3, 逻辑结果, 4, 0)
调试输出 ("  逻辑结果: " + 到文本 (逻辑结果))
获取调试信息 (调试信息, 2048)
调试输出 ("  检测类型: " + 提取检测类型 (调试信息))
调试输出 ("")

' 自动识别
调试输出 ("5. 🎯 自动识别 - 智能选择 (resultType=5)")
文本结果 = ""
调用成功 = 远程调用DLL (地址, "检测", "99,98,你好", 3, 文本结果, 5, 1024)
调试输出 ("  自动结果: [" + 文本结果 + "]")
获取调试信息 (调试信息, 2048)
调试输出 ("  检测类型: " + 提取检测类型 (调试信息))
调试输出 ("  🎉 系统自动选择了最合适的类型！")
调试输出 ("")

.子程序 提取检测类型, 文本型
.参数 调试信息, 文本型

.局部变量 检测类型位置, 整数型
.局部变量 检测类型, 文本型

检测类型位置 = 寻找文本 (调试信息, "DetectedResultType:", , 假)
如果 (检测类型位置 > 0)
    检测类型 = 取文本中间 (调试信息, 检测类型位置 + 19, 15)
    检测类型 = 取文本左边 (检测类型, 寻找文本 (检测类型, ",", , 假) - 1)
    返回 (检测类型)
否则
    返回 ("未知")
如果真结束

.子程序 测试不同函数名的识别

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型
.局部变量 函数名列表, 文本型, , "8"
.局部变量 i, 整数型

调试输出 ("=== 测试不同函数名的智能识别 ===")

' 准备不同类型的函数名
函数名列表 [1] = "检测"        ' 可能返回布尔值
函数名列表 [2] = "GetText"     ' 明显的文本函数
函数名列表 [3] = "GetVersion"  ' 版本信息，应该是文本
函数名列表 [4] = "IsValid"     ' Is开头，应该是布尔值
函数名列表 [5] = "HasData"     ' Has开头，应该是布尔值
函数名列表 [6] = "GetRate"     ' Rate，可能是浮点数
函数名列表 [7] = "Calculate"   ' 计算，可能是数字
函数名列表 [8] = "GetInfo"     ' Info，应该是文本

计次循环首 (8, i)
    调试输出 ("测试函数: " + 函数名列表 [i])
    
    ' 使用自动识别
    文本结果 = ""
    调用成功 = 远程调用DLL (地址, 函数名列表 [i], "test", 1, 文本结果, 5, 1024)
    
    获取调试信息 (调试信息, 2048)
    调试输出 ("  预测类型: " + 提取检测类型 (调试信息))
    调试输出 ("  结果: [" + 文本结果 + "]")
    调试输出 ("")
计次循环尾 ()

调试输出 ("=== 总结 ===")
调试输出 ("🎯 智能结果识别的优势：")
调试输出 ("1. 自动分析函数返回值，选择最合适的类型")
调试输出 ("2. 根据函数名模式预测返回类型")
调试输出 ("3. 分析实际返回值确定最佳处理方式")
调试输出 ("4. resultType=5时完全自动化，无需手动指定")
调试输出 ("5. 提供详细的检测过程信息")
调试输出 ("")
调试输出 ("💡 使用建议：")
调试输出 ("- 不确定返回类型时，使用 resultType=5 自动识别")
调试输出 ("- 查看调试信息中的 DetectedResultType 了解系统判断")
调试输出 ("- 根据函数名选择合适的命名模式以获得更好的识别效果")

.子程序 演示类型识别规则

调试输出 ("")
调试输出 ("=== 智能识别规则说明 ===")
调试输出 ("📋 函数名模式识别：")
调试输出 ("  Text/String/Get/Version/Info/Name/Path → 文本型")
调试输出 ("  Float/Rate/Percent/Ratio → 浮点型")
调试输出 ("  Is/Has/Can/Check/Test → 逻辑型")
调试输出 ("")
调试输出 ("📊 返回值分析：")
调试输出 ("  有效字符串指针 → 文本型")
调试输出 ("  0或1 → 逻辑型")
调试输出 ("  0-255 → 整数型")
调试输出 ("  大数值 → 长整数型")
调试输出 ("  默认 → 整数型")
调试输出 ("")
调试输出 ("🎯 自动选择策略：")
调试输出 ("  1. 先检查返回值是否为有效文本指针")
调试输出 ("  2. 再根据函数名模式预测类型")
调试输出 ("  3. 最后分析数值范围确定类型")
调试输出 ("  4. 选择最合适的类型进行转换")

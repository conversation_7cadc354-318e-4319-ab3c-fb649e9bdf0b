.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "test.dll"  ' 请替换为实际的DLL地址

调试输出 ("=== 测试安全版本RemoteCallDLL ===")
调试输出 ("版本特点：")
调试输出 ("1. 不会闪退 - 完整的异常处理")
调试输出 ("2. 支持任意参数数量和类型")
调试输出 ("3. 安全的内存访问")
调试输出 ("4. 详细的调试信息")
调试输出 ("")

' 测试您的具体问题
测试您的原始问题 ()

' 测试各种安全场景
测试安全性 ()

.子程序 测试您的原始问题

.局部变量 文本结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试您的原始问题（之前会闪退） ===")

' 初始化结果变量
文本结果 = ""

' 您的原始调用：远程调用DLL("test.dll", "文本测试", "1", 1, 文本结果, 3, 4096)
调用成功 = 远程调用DLL (地址, "文本测试", "1", 1, 文本结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 文本结果 + "]")
调试输出 ("文本结果长度: " + 到文本 (取文本长度 (文本结果)))

' 获取详细调试信息
获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试安全性

.局部变量 结果, 文本型
.局部变量 整数结果, 整数型
.局部变量 浮点结果, 小数型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试安全性（各种边界情况） ===")

' 测试1：空DLL路径
调试输出 ("测试1：空DLL路径")
调用成功 = 远程调用DLL ("", "函数", "", 0, 结果, 3, 1024)
调试输出 ("结果: " + 结果)
获取调试信息 (调试信息, 1024)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 测试2：不存在的DLL
调试输出 ("测试2：不存在的DLL")
调用成功 = 远程调用DLL ("不存在.dll", "函数", "", 0, 结果, 3, 1024)
调试输出 ("结果: " + 结果)
获取调试信息 (调试信息, 1024)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 测试3：不存在的函数
调试输出 ("测试3：不存在的函数")
调用成功 = 远程调用DLL (地址, "不存在的函数", "", 0, 结果, 3, 1024)
调试输出 ("结果: " + 结果)
获取调试信息 (调试信息, 1024)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 测试4：大量参数
调试输出 ("测试4：大量参数")
调用成功 = 远程调用DLL (地址, "多参数函数", "1,2,3,4,5,6,7,8,9,10", 10, 整数结果, 1, 0)
调试输出 ("整数结果: " + 到文本 (整数结果))
获取调试信息 (调试信息, 1024)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 测试5：混合参数类型
调试输出 ("测试5：混合参数类型")
调用成功 = 远程调用DLL (地址, "混合函数", "文本,123,3.14,真", 4, 结果, 3, 1024)
调试输出 ("结果: " + 结果)
获取调试信息 (调试信息, 1024)
调试输出 ("调试: " + 调试信息)
调试输出 ("")

' 测试6：不同返回类型
调试输出 ("测试6：不同返回类型测试")

' 整数返回
调用成功 = 远程调用DLL (地址, "整数函数", "100", 1, 整数结果, 1, 0)
调试输出 ("整数返回: " + 到文本 (整数结果))

' 浮点返回
调用成功 = 远程调用DLL (地址, "浮点函数", "3.14", 1, 浮点结果, 2, 0)
调试输出 ("浮点返回: " + 到文本 (浮点结果))

' 文本返回
调用成功 = 远程调用DLL (地址, "文本函数", "Hello", 1, 结果, 3, 1024)
调试输出 ("文本返回: " + 结果)

调试输出 ("")
调试输出 ("=== 安全性测试完成 ===")
调试输出 ("如果程序没有闪退，说明新版本非常稳定！")

.子程序 测试极限情况

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型
.局部变量 i, 整数型

调试输出 ("=== 测试极限情况 ===")

' 测试超长参数
调试输出 ("测试超长参数字符串")
调用成功 = 远程调用DLL (地址, "函数", "这是一个非常长的参数字符串，包含很多内容，用来测试参数解析的稳定性和安全性，看看会不会出现缓冲区溢出或其他问题", 1, 结果, 3, 1024)
调试输出 ("结果: " + 结果)

' 测试空参数但有参数数量
调试输出 ("测试空参数但有参数数量")
调用成功 = 远程调用DLL (地址, "函数", "", 5, 结果, 3, 1024)
调试输出 ("结果: " + 结果)
获取调试信息 (调试信息, 1024)
调试输出 ("调试: " + 调试信息)

' 测试参数数量不匹配
调试输出 ("测试参数数量不匹配")
调用成功 = 远程调用DLL (地址, "函数", "1,2", 5, 结果, 3, 1024)
调试输出 ("结果: " + 结果)

调试输出 ("")
调试输出 ("=== 极限测试完成 ===")

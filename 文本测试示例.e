.版本 2
.支持库 spec

.DLL命令 远程调用DLL, 逻辑型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型, , 远程DLL文件的URL地址或本地路径
    .参数 函数名称, 文本型, , 要调用的DLL函数名称
    .参数 参数字符串, 文本型, , 智能参数解析，支持多种类型
    .参数 参数个数, 整数型, , 函数参数的个数（0-8个）
    .参数 结果, 变体型, 传址, 接收结果的变量
    .参数 结果类型, 整数型, , 结果类型：0=无结果 1=整数 2=浮点 3=字符串 4=逻辑性 5=自动 6=长整数
    .参数 缓冲区大小, 整数型, , 字符串时需要，建议256

.DLL命令 获取调用结果整数, 整数型, "MyDll.dll", "GetLastCallResultInt", 公开
.DLL命令 获取调用结果浮点, 小数型, "MyDll.dll", "GetLastCallResultDouble", 公开
.DLL命令 获取调用结果字符串, 整数型, "MyDll.dll", "GetLastCallResultString", 公开
    .参数 缓冲区, 文本型, 传址, 接收结果的缓冲区
    .参数 缓冲区大小, 整数型, , 缓冲区大小
.DLL命令 获取调用结果类型, 整数型, "MyDll.dll", "GetLastCallResultType", 公开
.DLL命令 检查调用结果, 逻辑型, "MyDll.dll", "HasLastCallResult", 公开
.DLL命令 从指针提取文本, 整数型, "MyDll.dll", "ExtractTextFromResultInt", 公开
    .参数 缓冲区, 文本型, 传址, 接收结果的缓冲区
    .参数 缓冲区大小, 整数型, , 缓冲区大小

.子程序 _启动子程序()
    .局部变量 地址, 文本型
    .局部变量 调用成功, 逻辑型
    .局部变量 整数结果, 整数型
    .局部变量 文本结果, 文本型
    .局部变量 文本缓冲区, 文本型
    .局部变量 指针文本, 文本型
    
    // 使用要测试的DLL地址
    地址 = "http://127.0.0.1:5244/d/%E7%94%B5%E8%A7%86%E5%89%A7/1111111111111111111.dll?sign=wPJ1BM39OI94P9t9lGSR3pxuTH3oE2SLnD-jSbrlLIg=:0"
    
    调试输出("=========== 文本测试函数专用测试 ===========")
    调试输出("确保以下条件满足：")
    调试输出("1. 使用专门处理文本的DLL版本")
    调试输出("2. 远程DLL中含有'文本测试'函数")
    调试输出("3. 参数个数设置正确")
    调试输出("")
    
    // 测试1: 常规调用，获取指针值
    调试输出("------ 测试1: 文本测试函数 - 整数指针 ------")
    整数结果 = 0
    调用成功 = 远程调用DLL(地址, "文本测试", "", 0, 整数结果, 1, 0)
    调试输出("调用成功: " + 如果(调用成功, "是", "否"))
    调试输出("指针值: " + 到文本(整数结果) + " (这个数字是内存地址)")
    调试输出("")
    
    // 测试2: 直接使用文本型接收
    调试输出("------ 测试2: 文本测试函数 - 直接文本接收 ------")
    文本结果 = ""
    调用成功 = 远程调用DLL(地址, "文本测试", "", 0, 文本结果, 3, 4096)
    调试输出("调用成功: " + 如果(调用成功, "是", "否"))
    调试输出("文本结果: [" + 文本结果 + "]")
    调试输出("")
    
    // 测试3: 先获取指针，再转换为文本
    调试输出("------ 测试3: 指针转文本 - 特殊方法 ------")
    整数结果 = 0
    调用成功 = 远程调用DLL(地址, "文本测试", "", 0, 整数结果, 1, 0)
    如果 调用成功 = 真
        指针文本 = ""
        从指针提取文本(指针文本, 4096)
        调试输出("指针值: " + 到文本(整数结果))
        调试输出("提取文本: [" + 指针文本 + "]")
    否则
        调试输出("调用失败")
    如果结束
    调试输出("")
    
    // 测试4: 使用常规获取结果方法
    调试输出("------ 测试4: 常规获取结果文本 ------")
    调用成功 = 远程调用DLL(地址, "文本测试", "", 0, 0, 0, 0)
    如果 调用成功 = 真 且 检查调用结果() = 真
        调试输出("结果类型: " + 到文本(获取调用结果类型()))
        文本缓冲区 = ""
        获取调用结果字符串(文本缓冲区, 4096)
        调试输出("结果文本: [" + 文本缓冲区 + "]")
    否则
        调试输出("调用失败或无结果")
    如果结束
    调试输出("")
    
    // 测试5: 自动模式测试
    调试输出("------ 测试5: 自动模式测试 ------")
    文本结果 = ""
    调用成功 = 远程调用DLL(地址, "文本测试", "", 0, 文本结果, 5, 4096)
    调试输出("调用成功: " + 如果(调用成功, "是", "否"))
    调试输出("自动模式结果: [" + 文本结果 + "]")
    调试输出("")
    
    // 对照测试：加法函数
    调试输出("------ 对照测试: 常规加法函数 ------")
    整数结果 = 0
    调用成功 = 远程调用DLL(地址, "加法", "3,4", 2, 整数结果, 1, 0)
    调试输出("调用成功: " + 如果(调用成功, "是", "否"))
    调试输出("加法结果: " + 到文本(整数结果) + " (预期为7)")
    调试输出("")
    
    调试输出("=========== 测试总结 ===========")
    调试输出("如果您看到了文本测试的实际文本内容，说明新DLL正常工作！")
    调试输出("如果只看到数字7511648，则说明返回的仍然是指针值。")
    调试输出("推荐使用直接文本接收(测试2)或专门的指针提取函数(测试3)。")
结束 
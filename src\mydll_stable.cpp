#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <wininet.h>
#include <urlmon.h>
#include <clocale>
#include <stdio.h>
#include <string.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// Global variables
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;
static char g_DebugInfo[1024] = {0};

// Text function list
static const char* TEXT_FUNCTIONS[] = {
    "文本测试", "文本", "Text", "String", "GetText", "GetString", "Version", "测试", NULL
};

// Forward declarations
bool IsTextFunction(const char* functionName);
int SafeGetStringLength(const char* str, int maxLen);

// Check if function is text function
bool IsTextFunction(const char* functionName) {
    if (!functionName) return false;

    for (int i = 0; TEXT_FUNCTIONS[i] != NULL; i++) {
        if (strcmp(functionName, TEXT_FUNCTIONS[i]) == 0) {
            return true;
        }
    }

    if (strstr(functionName, "文本") || strstr(functionName, "Text") ||
        strstr(functionName, "String") || strstr(functionName, "Get") ||
        strstr(functionName, "Version")) {
        return true;
    }

    return false;
}

// Safe string length
int SafeGetStringLength(const char* str, int maxLen) {
    if (!str) return 0;

    __try {
        return strnlen(str, maxLen);
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        return 0;
    }
}

// Safe function call wrapper
DWORD_PTR SafeCallFunction(FARPROC procAddr, DWORD_PTR* args, int argCount, BOOL* success) {
    *success = FALSE;
    DWORD_PTR result = 0;

    __try {
        switch (argCount) {
            case 0: {
                typedef DWORD_PTR (__stdcall *Func0)();
                Func0 func = (Func0)procAddr;
                result = func();
                *success = TRUE;
                break;
            }
            case 1: {
                typedef DWORD_PTR (__stdcall *Func1)(DWORD_PTR);
                Func1 func = (Func1)procAddr;
                result = func(args[0]);
                *success = TRUE;
                break;
            }
            case 2: {
                typedef DWORD_PTR (__stdcall *Func2)(DWORD_PTR, DWORD_PTR);
                Func2 func = (Func2)procAddr;
                result = func(args[0], args[1]);
                *success = TRUE;
                break;
            }
            case 3: {
                typedef DWORD_PTR (__stdcall *Func3)(DWORD_PTR, DWORD_PTR, DWORD_PTR);
                Func3 func = (Func3)procAddr;
                result = func(args[0], args[1], args[2]);
                *success = TRUE;
                break;
            }
            default: {
                if (argCount <= 10) {
                    typedef DWORD_PTR (__stdcall *FuncGeneric)(DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR,
                                                               DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR);
                    FuncGeneric func = (FuncGeneric)procAddr;
                    result = func(args[0], args[1], args[2], args[3], args[4],
                                  args[5], args[6], args[7], args[8], args[9]);
                    *success = TRUE;
                }
                break;
            }
        }
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        result = 0;
        *success = FALSE;
    }

    return result;
}

// Save config
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    return 1;
}

// Load config
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    return 0;
}

// Program delay
MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

// Get last call result int
MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

// Get last call result double
MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

// Get last call result string
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    int len = strlen(g_LastCallResultString);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

    if (copyLen > 0) {
        memcpy(buffer, g_LastCallResultString, copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Get last call result type
MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

// Has last call result
MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

// Extract text from result int
MYDLL_API int __stdcall ExtractTextFromResultInt(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0 || !g_LastCallHasResult) return 0;

    const char* strPtr = reinterpret_cast<const char*>(g_LastCallResult);
    int length = SafeGetStringLength(strPtr, 1024);

    if (length > 0) {
        int copyLen = (length < bufferSize - 1) ? length : bufferSize - 1;
        if (copyLen > 0) {
            memcpy(buffer, strPtr, copyLen);
        }
        buffer[copyLen] = '\0';
        return copyLen;
    }

    sprintf_s(buffer, bufferSize, "Pointer: %p", strPtr);
    return strlen(buffer);
}

// Get debug info
MYDLL_API int __stdcall GetDebugInfo(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    int len = strlen(g_DebugInfo);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

    if (copyLen > 0) {
        memcpy(buffer, g_DebugInfo, copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Stable RemoteCallDLL - No crashes, supports all parameter types
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName,
                                      const char* parameters, int paramCount,
                                      void* result, int resultType, int bufferSize)
{
    // Initialize result safely
    if (result) {
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                memset(result, 0, bufferSize);
            }
        } else {
            memset(result, 0, sizeof(int));
        }
    }

    if (!remotePath || !functionName || !result) {
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Invalid parameters");
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;

    // Handle remote download
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());

        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5 && bufferSize > 0) {
                strcpy_s((char*)result, bufferSize, "Download failed");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
            sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Download failed: %s", remotePath);
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }

    // Load DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5 && bufferSize > 0) {
            sprintf_s((char*)result, bufferSize, "Load failed: %d", error);
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed: %d", error);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "LoadLibrary failed: %s, Error: %d", dllPath, error);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Get function address
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5 && bufferSize > 0) {
            sprintf_s((char*)result, bufferSize, "Function not found: %s", functionName);
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found: %s", functionName);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "GetProcAddress failed: %s, Error: %d", functionName, error);
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Parse parameters
    DWORD_PTR args[16] = {0};
    int actualParamCount = 0;

    if (parameters && strlen(parameters) > 0 && paramCount > 0) {
        char paramsCopy[1024];
        strcpy_s(paramsCopy, sizeof(paramsCopy), parameters);

        char* token = strtok(paramsCopy, ",");
        while (token && actualParamCount < 16 && actualParamCount < paramCount) {
            // Trim spaces
            while (*token == ' ') token++;
            char* end = token + strlen(token) - 1;
            while (end > token && *end == ' ') *end-- = '\0';

            // Convert parameter
            if (strlen(token) > 0) {
                char* endptr;
                long longVal = strtol(token, &endptr, 10);
                if (*endptr == '\0') {
                    args[actualParamCount] = (DWORD_PTR)longVal;
                } else {
                    args[actualParamCount] = (DWORD_PTR)token;
                }
                actualParamCount++;
            }
            token = strtok(NULL, ",");
        }
    }

    // Call function safely
    BOOL callSuccess = FALSE;
    DWORD_PTR functionResult = SafeCallFunction(procAddr, args, actualParamCount, &callSuccess);

    // Try alternative calling if first attempt failed
    if (!callSuccess && actualParamCount == 1 && parameters) {
        // Try with string parameter
        args[0] = (DWORD_PTR)parameters;
        functionResult = SafeCallFunction(procAddr, args, 1, &callSuccess);
    }

    // Process result
    if (callSuccess) {
        // Try to extract text from result if it looks like a pointer
        char textResult[1024] = {0};
        BOOL hasText = FALSE;

        if (functionResult != 0) {
            const char* strPtr = reinterpret_cast<const char*>(functionResult);
            int textLen = SafeGetStringLength(strPtr, 1024);
            if (textLen > 0) {
                strncpy_s(textResult, sizeof(textResult), strPtr, textLen);
                hasText = TRUE;
            }
        }

        // Set result based on type
        switch (resultType) {
            case 1: // Integer
                *(int*)result = (int)functionResult;
                break;
            case 2: // Double
                *(double*)result = (double)functionResult;
                g_LastCallResultDouble = (double)functionResult;
                break;
            case 3: // String
            case 5: // Auto
                if (bufferSize > 0) {
                    if (hasText) {
                        strcpy_s((char*)result, bufferSize, textResult);
                    } else {
                        sprintf_s((char*)result, bufferSize, "%d", (int)functionResult);
                    }
                }
                break;
            case 4: // Boolean
                *(BOOL*)result = (functionResult != 0);
                break;
            case 6: // Long integer
                *(long long*)result = (long long)functionResult;
                break;
            default:
                *(int*)result = (int)functionResult;
                break;
        }

        // Store to global variables
        if (hasText) {
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult);
            g_LastCallResultType = 2; // Text type
        } else {
            sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", (int)functionResult);
            g_LastCallResultType = 0; // Numeric type
        }

        g_LastCallResult = (int)functionResult;
        g_LastCallHasResult = TRUE;

        // Debug info
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Function: %s, Params: [%s], ParamCount: %d->%d, Result: 0x%X, HasText: %s, Success: Yes",
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount,
            (unsigned int)functionResult, hasText ? "Yes" : "No");
    } else {
        // Call failed
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                strcpy_s((char*)result, bufferSize, "Call failed");
            }
        } else {
            *(int*)result = -1;
        }

        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Function: %s, Params: [%s], ParamCount: %d->%d, Success: No",
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount);
    }

    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);

    return callSuccess ? 1 : 0;
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        std::setlocale(LC_ALL, "C");
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        memset(g_DebugInfo, 0, sizeof(g_DebugInfo));
        break;
    }
    return TRUE;
}

#define MYDLL_EXPORTS
#include "mydll.h"
#include <windows.h>
#include <wininet.h>
#include <urlmon.h>
#include <clocale>
#include <stdio.h>
#include <string.h>
#include <math.h>
#include <ctype.h>

#pragma comment(lib, "wininet.lib")
#pragma comment(lib, "urlmon.lib")

// Global variables
static int g_LastCallResult = 0;
static double g_LastCallResultDouble = 0.0;
static char g_LastCallResultString[1024] = {0};
static BOOL g_LastCallHasResult = FALSE;
static int g_LastCallResultType = 0;
static char g_DebugInfo[1024] = {0};

// Text function list
static const char* TEXT_FUNCTIONS[] = {
    "文本测试", "文本", "Text", "String", "GetText", "GetString", "Version", "测试", NULL
};

// Forward declarations
bool IsTextFunction(const char* functionName);
int SafeGetStringLength(const char* str, int maxLen);

// Check if function is text function
bool IsTextFunction(const char* functionName) {
    if (!functionName) return false;

    for (int i = 0; TEXT_FUNCTIONS[i] != NULL; i++) {
        if (strcmp(functionName, TEXT_FUNCTIONS[i]) == 0) {
            return true;
        }
    }

    if (strstr(functionName, "文本") || strstr(functionName, "Text") ||
        strstr(functionName, "String") || strstr(functionName, "Get") ||
        strstr(functionName, "Version")) {
        return true;
    }

    return false;
}

// Safe string length
int SafeGetStringLength(const char* str, int maxLen) {
    if (!str) return 0;

    __try {
        return strnlen(str, maxLen);
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        return 0;
    }
}

// Advanced parameter type detection
int DetectParameterType(const char* param) {
    if (!param || strlen(param) == 0) return 1; // Empty = string

    // Check for array notation {1,2,3} or [1,2,3]
    if ((param[0] == '{' && param[strlen(param)-1] == '}') ||
        (param[0] == '[' && param[strlen(param)-1] == ']')) {
        return 12; // Array
    }

    // Check for pointer notation 0x12345678
    if (strncmp(param, "0x", 2) == 0 || strncmp(param, "0X", 2) == 0) {
        return 11; // Pointer
    }

    // Check for boolean values
    if (strcmp(param, "true") == 0 || strcmp(param, "false") == 0 ||
        strcmp(param, "True") == 0 || strcmp(param, "False") == 0 ||
        strcmp(param, "1") == 0 || strcmp(param, "0") == 0) {
        return 4; // Boolean
    }

    // Check for date/time format YYYY-MM-DD or YYYY/MM/DD
    if (strlen(param) >= 8 && (strchr(param, '-') || strchr(param, '/'))) {
        int year, month, day;
        if (sscanf_s(param, "%d-%d-%d", &year, &month, &day) == 3 ||
            sscanf_s(param, "%d/%d/%d", &year, &month, &day) == 3) {
            if (year >= 1900 && year <= 2100 && month >= 1 && month <= 12 && day >= 1 && day <= 31) {
                return 8; // DateTime
            }
        }
    }

    char* endptr;

    // Try long integer first (for very large numbers)
    if (strlen(param) > 9) {
        long long longVal = _strtoi64(param, &endptr, 10);
        if (*endptr == '\0') {
            return 7; // Long integer
        }
    }

    // Try integer
    long intVal = strtol(param, &endptr, 10);
    if (*endptr == '\0') {
        // Check range for different integer types
        if (intVal >= 0 && intVal <= 255) {
            return 5; // Could be byte, but default to int
        } else if (intVal >= -32768 && intVal <= 32767) {
            return 6; // Could be short, but default to int
        }
        return 0; // Integer
    }

    // Try double precision float
    double doubleVal = strtod(param, &endptr);
    if (*endptr == '\0') {
        // Check if it needs double precision
        if (strlen(param) > 7 || doubleVal > 1e6 || doubleVal < 1e-6) {
            return 3; // Double precision
        }
        return 2; // Float
    }

    // Check for function pointer notation func@address
    if (strchr(param, '@') != NULL) {
        return 10; // Function pointer
    }

    // Check for byte array notation (hex string)
    if (strlen(param) % 2 == 0) {
        bool isHex = true;
        for (size_t i = 0; i < strlen(param); i++) {
            if (!isxdigit(param[i])) {
                isHex = false;
                break;
            }
        }
        if (isHex && strlen(param) > 2) {
            return 9; // Byte array
        }
    }

    // Default to string
    return 1; // String
}

// Get type name for debugging
const char* GetTypeName(int typeCode) {
    switch (typeCode) {
        case 0: return "int";
        case 1: return "str";
        case 2: return "float";
        case 3: return "double";
        case 4: return "bool";
        case 5: return "byte";
        case 6: return "short";
        case 7: return "long";
        case 8: return "datetime";
        case 9: return "bytes";
        case 10: return "funcptr";
        case 11: return "pointer";
        case 12: return "array";
        case 13: return "auto";
        default: return "unknown";
    }
}

// Revolutionary Parameter Description Parser - User's Innovation
int ParseParameterDescription(const char* paramDesc, DWORD_PTR* args, int* paramTypes,
                             char** stringParams, int maxParams) {
    if (!paramDesc || strlen(paramDesc) == 0) return 0;

    int paramCount = 0;
    char descCopy[2048];
    strcpy_s(descCopy, sizeof(descCopy), paramDesc);

    // Check if it's the new description format (support both Chinese and English)
    if (strstr(descCopy, "str:") != NULL || strstr(descCopy, "int:") != NULL ||
        strstr(descCopy, "float:") != NULL || strstr(descCopy, "bool:") != NULL ||
        strstr(descCopy, "型：") != NULL) {
        // New format: "str:Hello,int:123,float:3.14"
        char* token = strtok(descCopy, ",");

        while (token && paramCount < maxParams) {
            // Trim spaces
            while (*token == ' ') token++;
            char* end = token + strlen(token) - 1;
            while (end > token && *end == ' ') *end-- = '\0';

            // Parse type description
            if (strstr(token, "str:") == token) {
                paramTypes[paramCount] = 1; // String
                char* value = token + 4; // Skip "str:"
                stringParams[paramCount] = _strdup(value);
                args[paramCount] = (DWORD_PTR)stringParams[paramCount];
                paramCount++;
            }
            else if (strstr(token, "int:") == token) {
                paramTypes[paramCount] = 0; // Integer
                char* value = token + 4; // Skip "int:"
                int intVal = atoi(value);
                args[paramCount] = (DWORD_PTR)intVal;
                paramCount++;
            }
            else if (strstr(token, "float:") == token) {
                paramTypes[paramCount] = 2; // Float
                char* value = token + 6; // Skip "float:"
                float floatVal = (float)atof(value);
                args[paramCount] = *(DWORD_PTR*)&floatVal;
                paramCount++;
            }
            else if (strstr(token, "double:") == token) {
                paramTypes[paramCount] = 3; // Double
                char* value = token + 7; // Skip "double:"
                double doubleVal = atof(value);
                args[paramCount] = *(DWORD_PTR*)&doubleVal;
                paramCount++;
            }
            else if (strstr(token, "bool:") == token) {
                paramTypes[paramCount] = 4; // Boolean
                char* value = token + 5; // Skip "bool:"
                bool boolVal = (strcmp(value, "true") == 0 || strcmp(value, "1") == 0);
                args[paramCount] = (DWORD_PTR)boolVal;
                paramCount++;
            }
            else if (strstr(token, "long:") == token) {
                paramTypes[paramCount] = 7; // Long integer
                char* value = token + 5; // Skip "long:"
                long long longVal = _strtoi64(value, NULL, 10);
                args[paramCount] = (DWORD_PTR)longVal;
                paramCount++;
            }
            else if (strstr(token, "short:") == token) {
                paramTypes[paramCount] = 6; // Short integer
                char* value = token + 6; // Skip "short:"
                short shortVal = (short)atoi(value);
                args[paramCount] = (DWORD_PTR)shortVal;
                paramCount++;
            }
            else if (strstr(token, "byte:") == token) {
                paramTypes[paramCount] = 5; // Byte
                char* value = token + 5; // Skip "byte:"
                unsigned char byteVal = (unsigned char)atoi(value);
                args[paramCount] = (DWORD_PTR)byteVal;
                paramCount++;
            }
            else if (strstr(token, "bytes:") == token) {
                paramTypes[paramCount] = 9; // Byte array
                char* value = token + 6; // Skip "bytes:"
                stringParams[paramCount] = _strdup(value);
                args[paramCount] = (DWORD_PTR)stringParams[paramCount];
                paramCount++;
            }
            else if (strstr(token, "date:") == token) {
                paramTypes[paramCount] = 8; // DateTime
                char* value = token + 5; // Skip "date:"
                stringParams[paramCount] = _strdup(value);
                args[paramCount] = (DWORD_PTR)stringParams[paramCount];
                paramCount++;
            }
            else if (strstr(token, "func:") == token) {
                paramTypes[paramCount] = 10; // Function pointer
                char* value = token + 5; // Skip "func:"
                stringParams[paramCount] = _strdup(value);
                args[paramCount] = (DWORD_PTR)stringParams[paramCount];
                paramCount++;
            }

            token = strtok(NULL, ",");
        }
    } else {
        // Fallback to old comma-separated format
        char* token = strtok(descCopy, ",");
        while (token && paramCount < maxParams) {
            // Trim spaces
            while (*token == ' ') token++;
            char* end = token + strlen(token) - 1;
            while (end > token && *end == ' ') *end-- = '\0';

            if (strlen(token) > 0) {
                paramTypes[paramCount] = DetectParameterType(token);

                // Convert based on detected type (existing logic)
                switch (paramTypes[paramCount]) {
                    case 0: case 5: case 6: // Integer, Byte, Short
                        args[paramCount] = (DWORD_PTR)strtol(token, NULL, 10);
                        break;
                    case 7: // Long integer
                        args[paramCount] = (DWORD_PTR)_strtoi64(token, NULL, 10);
                        break;
                    case 2: // Float
                        {
                            float val = (float)strtod(token, NULL);
                            args[paramCount] = *(DWORD_PTR*)&val;
                        }
                        break;
                    case 3: // Double
                        {
                            double val = strtod(token, NULL);
                            args[paramCount] = *(DWORD_PTR*)&val;
                        }
                        break;
                    case 4: // Boolean
                        args[paramCount] = (DWORD_PTR)(strcmp(token, "true") == 0 || strcmp(token, "True") == 0 || strcmp(token, "1") == 0);
                        break;
                    case 11: // Pointer
                        args[paramCount] = (DWORD_PTR)strtoul(token, NULL, 16);
                        break;
                    default: // String and others
                        stringParams[paramCount] = _strdup(token);
                        args[paramCount] = (DWORD_PTR)stringParams[paramCount];
                        break;
                }
                paramCount++;
            }
            token = strtok(NULL, ",");
        }
    }

    return paramCount;
}

// Intelligent result type detection based on function result
int DetectResultType(DWORD_PTR functionResult, const char* functionName) {
    // Check if result looks like a valid string pointer
    if (functionResult != 0) {
        const char* strPtr = reinterpret_cast<const char*>(functionResult);
        int textLen = SafeGetStringLength(strPtr, 1024);

        // If we can read valid text from the pointer
        if (textLen > 0 && textLen < 1024) {
            // Check if it's printable ASCII or UTF-8
            bool isPrintable = true;
            for (int i = 0; i < textLen && i < 100; i++) {
                unsigned char c = strPtr[i];
                if (c < 32 && c != '\t' && c != '\n' && c != '\r') {
                    isPrintable = false;
                    break;
                }
            }
            if (isPrintable) {
                return 3; // String result
            }
        }
    }

    // Check function name patterns for expected return types
    if (functionName) {
        // Text function patterns
        if (strstr(functionName, "Text") || strstr(functionName, "String") ||
            strstr(functionName, "Get") || strstr(functionName, "Version") ||
            strstr(functionName, "Info") || strstr(functionName, "Name") ||
            strstr(functionName, "Path")) {
            return 3; // String result expected
        }

        // Float function patterns
        if (strstr(functionName, "Float") || strstr(functionName, "Rate") ||
            strstr(functionName, "Percent") || strstr(functionName, "Ratio")) {
            return 2; // Float result expected
        }

        // Boolean function patterns
        if (strstr(functionName, "Is") || strstr(functionName, "Has") ||
            strstr(functionName, "Can") || strstr(functionName, "Check") ||
            strstr(functionName, "Test")) {
            return 4; // Boolean result expected
        }
    }

    // Analyze the numeric value
    int intResult = (int)functionResult;

    // Check if it's a boolean-like result
    if (intResult == 0 || intResult == 1) {
        return 4; // Boolean (could be true/false)
    }

    // Check if it's in byte range
    if (intResult >= 0 && intResult <= 255) {
        return 1; // Could be byte, but return as integer
    }

    // Check if it's a large number (might be pointer or long)
    if (intResult > 1000000 || intResult < -1000000) {
        return 6; // Long integer
    }

    // Default to integer
    return 1; // Integer result
}

// Smart result processing with automatic type detection
void ProcessSmartResult(DWORD_PTR functionResult, const char* functionName,
                       void* result, int requestedResultType, int bufferSize,
                       char* textResult, BOOL* hasText, int* detectedType) {

    *hasText = FALSE;
    *detectedType = DetectResultType(functionResult, functionName);

    // If user requested auto detection (type 5), use detected type
    int actualResultType = (requestedResultType == 5) ? *detectedType : requestedResultType;

    // Try to extract text from result if it looks like a pointer
    if (functionResult != 0) {
        const char* strPtr = reinterpret_cast<const char*>(functionResult);
        int textLen = SafeGetStringLength(strPtr, 1024);
        if (textLen > 0 && textLen < 1024) {
            strncpy_s(textResult, 1024, strPtr, textLen);
            textResult[textLen] = '\0';
            *hasText = TRUE;
        }
    }

    // Process result based on actual type
    switch (actualResultType) {
        case 1: // Integer
            *(int*)result = (int)functionResult;
            break;

        case 2: // Float
            {
                float floatVal = (float)functionResult;
                // If we have text that looks like a float, parse it
                if (*hasText) {
                    char* endptr;
                    float parsedFloat = strtof(textResult, &endptr);
                    if (*endptr == '\0') {
                        floatVal = parsedFloat;
                    }
                }
                *(float*)result = floatVal;
            }
            break;

        case 3: // String
            if (bufferSize > 0) {
                if (*hasText) {
                    // Use extracted text
                    int copyLen = (strlen(textResult) < bufferSize - 1) ? strlen(textResult) : bufferSize - 1;
                    if (copyLen > 0) {
                        memcpy((char*)result, textResult, copyLen);
                    }
                    ((char*)result)[copyLen] = '\0';
                } else {
                    // Convert number to string
                    sprintf_s((char*)result, bufferSize, "%d", (int)functionResult);
                }
            }
            break;

        case 4: // Boolean
            {
                BOOL boolVal = (functionResult != 0);
                // If we have text, check for boolean strings
                if (*hasText) {
                    if (strcmp(textResult, "true") == 0 || strcmp(textResult, "True") == 0 ||
                        strcmp(textResult, "1") == 0) {
                        boolVal = TRUE;
                    } else if (strcmp(textResult, "false") == 0 || strcmp(textResult, "False") == 0 ||
                               strcmp(textResult, "0") == 0) {
                        boolVal = FALSE;
                    }
                }
                *(BOOL*)result = boolVal;
            }
            break;

        case 6: // Long integer
            *(long long*)result = (long long)functionResult;
            break;

        default:
            // Unknown type, default to integer
            *(int*)result = (int)functionResult;
            break;
    }
}

// Safe function call wrapper
DWORD_PTR SafeCallFunction(FARPROC procAddr, DWORD_PTR* args, int argCount, BOOL* success) {
    *success = FALSE;
    DWORD_PTR result = 0;

    __try {
        switch (argCount) {
            case 0: {
                typedef DWORD_PTR (__stdcall *Func0)();
                Func0 func = (Func0)procAddr;
                result = func();
                *success = TRUE;
                break;
            }
            case 1: {
                typedef DWORD_PTR (__stdcall *Func1)(DWORD_PTR);
                Func1 func = (Func1)procAddr;
                result = func(args[0]);
                *success = TRUE;
                break;
            }
            case 2: {
                typedef DWORD_PTR (__stdcall *Func2)(DWORD_PTR, DWORD_PTR);
                Func2 func = (Func2)procAddr;
                result = func(args[0], args[1]);
                *success = TRUE;
                break;
            }
            case 3: {
                typedef DWORD_PTR (__stdcall *Func3)(DWORD_PTR, DWORD_PTR, DWORD_PTR);
                Func3 func = (Func3)procAddr;
                result = func(args[0], args[1], args[2]);
                *success = TRUE;
                break;
            }
            default: {
                if (argCount <= 10) {
                    typedef DWORD_PTR (__stdcall *FuncGeneric)(DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR,
                                                               DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR, DWORD_PTR);
                    FuncGeneric func = (FuncGeneric)procAddr;
                    result = func(args[0], args[1], args[2], args[3], args[4],
                                  args[5], args[6], args[7], args[8], args[9]);
                    *success = TRUE;
                }
                break;
            }
        }
    } __except(EXCEPTION_EXECUTE_HANDLER) {
        result = 0;
        *success = FALSE;
    }

    return result;
}

// Save config
MYDLL_API int __stdcall SaveConfig(const char* configFile, long parentWindow) {
    return 1;
}

// Load config
MYDLL_API int __stdcall LoadConfig(const char* configFile, long parentWindow) {
    return 0;
}

// Program delay
MYDLL_API int __stdcall ProgramDelay(int delayTime) {
    if (delayTime > 0) Sleep(delayTime);
    return 1;
}

// Get last call result int
MYDLL_API int __stdcall GetLastCallResultInt() {
    return g_LastCallResult;
}

// Get last call result double
MYDLL_API double __stdcall GetLastCallResultDouble() {
    return g_LastCallResultDouble;
}

// Get last call result string
MYDLL_API int __stdcall GetLastCallResultString(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    int len = strlen(g_LastCallResultString);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

    if (copyLen > 0) {
        memcpy(buffer, g_LastCallResultString, copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Get last call result type
MYDLL_API int __stdcall GetLastCallResultType() {
    return g_LastCallResultType;
}

// Has last call result
MYDLL_API int __stdcall HasLastCallResult() {
    return g_LastCallHasResult ? 1 : 0;
}

// Extract text from result int
MYDLL_API int __stdcall ExtractTextFromResultInt(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0 || !g_LastCallHasResult) return 0;

    const char* strPtr = reinterpret_cast<const char*>(g_LastCallResult);
    int length = SafeGetStringLength(strPtr, 1024);

    if (length > 0) {
        int copyLen = (length < bufferSize - 1) ? length : bufferSize - 1;
        if (copyLen > 0) {
            memcpy(buffer, strPtr, copyLen);
        }
        buffer[copyLen] = '\0';
        return copyLen;
    }

    sprintf_s(buffer, bufferSize, "Pointer: %p", strPtr);
    return strlen(buffer);
}

// Get debug info
MYDLL_API int __stdcall GetDebugInfo(char* buffer, int bufferSize) {
    if (!buffer || bufferSize <= 0) return 0;

    int len = strlen(g_DebugInfo);
    int copyLen = (len < bufferSize - 1) ? len : bufferSize - 1;

    if (copyLen > 0) {
        memcpy(buffer, g_DebugInfo, copyLen);
    }
    buffer[copyLen] = '\0';

    return copyLen;
}

// Stable RemoteCallDLL - No crashes, supports all parameter types
MYDLL_API int __stdcall RemoteCallDLL(const char* remotePath, const char* functionName,
                                      const char* parameters, int paramCount,
                                      void* result, int resultType, int bufferSize)
{
    // Initialize result safely
    if (result) {
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                memset(result, 0, bufferSize);
            }
        } else {
            memset(result, 0, sizeof(int));
        }
    }

    if (!remotePath || !functionName || !result) {
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Invalid parameters");
        return 0;
    }

    char dllPath[MAX_PATH];
    BOOL needCleanup = FALSE;

    // Handle remote download
    if (strstr(remotePath, "http://") || strstr(remotePath, "https://")) {
        char tempDir[MAX_PATH];
        GetTempPathA(MAX_PATH, tempDir);
        sprintf_s(dllPath, MAX_PATH, "%s\\remote_dll_%d.dll", tempDir, GetTickCount());

        HRESULT hr = URLDownloadToFileA(NULL, remotePath, dllPath, 0, NULL);
        if (FAILED(hr)) {
            if (resultType == 3 || resultType == 5 && bufferSize > 0) {
                strcpy_s((char*)result, bufferSize, "Download failed");
            } else {
                *(int*)result = -1;
            }
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Download failed");
            sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Download failed: %s", remotePath);
            return 0;
        }
        needCleanup = TRUE;
    } else {
        strcpy_s(dllPath, MAX_PATH, remotePath);
    }

    // Load DLL
    HMODULE hDll = LoadLibraryA(dllPath);
    if (!hDll) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5 && bufferSize > 0) {
            sprintf_s((char*)result, bufferSize, "Load failed: %d", error);
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Load failed: %d", error);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "LoadLibrary failed: %s, Error: %d", dllPath, error);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Get function address
    FARPROC procAddr = GetProcAddress(hDll, functionName);
    if (!procAddr) {
        DWORD error = GetLastError();
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                sprintf_s((char*)result, bufferSize, "Function not found: %s", functionName);
            }
        } else {
            *(int*)result = -1;
        }
        sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "Function not found: %s", functionName);
        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo), "Function '%s' not found in DLL, Error: %d", functionName, error);
        FreeLibrary(hDll);
        if (needCleanup) DeleteFileA(dllPath);
        return 0;
    }

    // Revolutionary Parameter Description System - User's Innovation
    DWORD_PTR args[16] = {0};
    char* stringParams[16] = {0};  // Store string parameters separately
    int paramTypes[16] = {0};      // Enhanced type system
    /*
    New Parameter Description Format:
    "文本型：Hello，整数型：123，小数型：3.14，逻辑型：真，长整数型：999999999"
    "子程序指针：第一个参数文本型：，第二个参数整数型：，第三个参数小数型："

    Type codes:
    0 = 整数型 (int)           1 = 文本型 (string)         2 = 小数型 (float)
    3 = 双精度小数型 (double)   4 = 逻辑型 (bool)          5 = 字节型 (byte)
    6 = 短整数型 (short)       7 = 长整数型 (long)        8 = 日期时间型 (datetime)
    9 = 字节集 (bytes)         10 = 子程序指针 (funcptr)   11 = 指针 (pointer)
    12 = 数组 (array)          13 = 自动识别 (auto)
    */
    int actualParamCount = 0;

    // Use revolutionary parameter description parser
    if (parameters && strlen(parameters) > 0) {
        actualParamCount = ParseParameterDescription(parameters, args, paramTypes, stringParams, 16);
    }

    // If paramCount is provided and different from parsed count, use the smaller one for safety
    if (paramCount > 0 && paramCount != actualParamCount) {
        actualParamCount = (paramCount < actualParamCount) ? paramCount : actualParamCount;
    }

    // Enhanced function calling with multiple type attempts
    BOOL callSuccess = FALSE;
    DWORD_PTR functionResult = 0;
    char callAttempts[512] = {0};
    int attemptCount = 0;

    // Attempt 1: Use detected types
    attemptCount++;
    functionResult = SafeCallFunction(procAddr, args, actualParamCount, &callSuccess);
    sprintf_s(callAttempts, sizeof(callAttempts), "Attempt1(detected-types):%s", callSuccess ? "OK" : "FAIL");

    // Attempt 2: If failed, try all as integers
    if (!callSuccess && actualParamCount > 0) {
        attemptCount++;
        DWORD_PTR intArgs[16] = {0};
        for (int i = 0; i < actualParamCount; i++) {
            if (paramTypes[i] == 1 && stringParams[i]) {
                // Convert string to integer if possible
                char* endptr;
                long val = strtol(stringParams[i], &endptr, 10);
                intArgs[i] = (*endptr == '\0') ? (DWORD_PTR)val : (DWORD_PTR)stringParams[i];
            } else {
                intArgs[i] = args[i];
            }
        }
        functionResult = SafeCallFunction(procAddr, intArgs, actualParamCount, &callSuccess);
        strcat_s(callAttempts, sizeof(callAttempts), callSuccess ? ", Attempt2(all-int):OK" : ", Attempt2(all-int):FAIL");
    }

    // Attempt 3: If still failed, try all as strings
    if (!callSuccess && actualParamCount > 0) {
        attemptCount++;
        DWORD_PTR strArgs[16] = {0};
        for (int i = 0; i < actualParamCount; i++) {
            if (paramTypes[i] == 0) {
                // Convert integer back to string
                static char intToStr[16][32];
                sprintf_s(intToStr[i], sizeof(intToStr[i]), "%d", (int)args[i]);
                strArgs[i] = (DWORD_PTR)intToStr[i];
            } else {
                strArgs[i] = args[i];
            }
        }
        functionResult = SafeCallFunction(procAddr, strArgs, actualParamCount, &callSuccess);
        strcat_s(callAttempts, sizeof(callAttempts), callSuccess ? ", Attempt3(all-str):OK" : ", Attempt3(all-str):FAIL");
    }

    // Attempt 4: Original parameter string as single parameter
    if (!callSuccess && actualParamCount == 1 && parameters) {
        attemptCount++;
        args[0] = (DWORD_PTR)parameters;
        functionResult = SafeCallFunction(procAddr, args, 1, &callSuccess);
        strcat_s(callAttempts, sizeof(callAttempts), callSuccess ? ", Attempt4(raw-str):OK" : ", Attempt4(raw-str):FAIL");
    }

    // Smart result processing with automatic type detection
    if (callSuccess) {
        char textResult[1024] = {0};
        BOOL hasText = FALSE;
        int detectedType = 0;

        // Use smart result processing
        ProcessSmartResult(functionResult, functionName, result, resultType, bufferSize,
                          textResult, &hasText, &detectedType);

        // Store to global variables
        if (hasText) {
            strcpy_s(g_LastCallResultString, sizeof(g_LastCallResultString), textResult);
            g_LastCallResultType = 2; // Text type
        } else {
            sprintf_s(g_LastCallResultString, sizeof(g_LastCallResultString), "%d", (int)functionResult);
            g_LastCallResultType = 0; // Numeric type
        }

        g_LastCallResult = (int)functionResult;
        g_LastCallResultDouble = (double)functionResult;
        g_LastCallHasResult = TRUE;

        // Enhanced debug info with complete parameter type details
        char paramTypeInfo[512] = {0};
        if (actualParamCount > 0) {
            sprintf_s(paramTypeInfo, sizeof(paramTypeInfo), "ParamTypes: ");
            for (int i = 0; i < actualParamCount && i < 8; i++) {
                char temp[64];
                sprintf_s(temp, sizeof(temp), "[%d]=%s ", i, GetTypeName(paramTypes[i]));
                strcat_s(paramTypeInfo, sizeof(paramTypeInfo), temp);
            }
        }

        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Function: %s, RawParams: [%s], ParamCount: %d->%d, %s, CallAttempts: %s, Result: 0x%X, DetectedResultType: %s, RequestedType: %d, HasText: %s, Success: Yes",
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount,
            paramTypeInfo, callAttempts, (unsigned int)functionResult, GetTypeName(detectedType), resultType, hasText ? "Yes" : "No");
    } else {
        // Call failed
        if (resultType == 3 || resultType == 5) {
            if (bufferSize > 0) {
                strcpy_s((char*)result, bufferSize, "Call failed");
            }
        } else {
            *(int*)result = -1;
        }

        sprintf_s(g_DebugInfo, sizeof(g_DebugInfo),
            "Function: %s, Params: [%s], ParamCount: %d->%d, Success: No",
            functionName, parameters ? parameters : "NULL", paramCount, actualParamCount);
    }

    FreeLibrary(hDll);
    if (needCleanup) DeleteFileA(dllPath);

    return callSuccess ? 1 : 0;
}

// DLL entry point
BOOL APIENTRY DllMain(HMODULE hModule, DWORD ul_reason_for_call, LPVOID lpReserved) {
    switch (ul_reason_for_call) {
    case DLL_PROCESS_ATTACH:
        std::setlocale(LC_ALL, "C");
        g_LastCallResult = 0;
        g_LastCallResultDouble = 0.0;
        memset(g_LastCallResultString, 0, sizeof(g_LastCallResultString));
        g_LastCallHasResult = FALSE;
        g_LastCallResultType = 0;
        memset(g_DebugInfo, 0, sizeof(g_DebugInfo));
        break;
    }
    return TRUE;
}

.版本 2

.DLL命令 远程调用DLL, 整数型, "MyDll.dll", "RemoteCallDLL", 公开
    .参数 远程路径, 文本型
    .参数 函数名, 文本型
    .参数 参数, 文本型
    .参数 参数个数, 整数型
    .参数 结果, 任意型, 传址
    .参数 结果类型, 整数型
    .参数 缓冲区大小, 整数型

.DLL命令 获取调试信息, 整数型, "MyDll.dll", "GetDebugInfo", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.DLL命令 获取上次调用字符串结果, 整数型, "MyDll.dll", "GetLastCallResultString", 公开
    .参数 缓冲区, 文本型, 传址
    .参数 缓冲区大小, 整数型

.程序集 窗口程序集1

.程序集变量 地址, 文本型

.子程序 _启动子程序, , , 本子程序在程序启动后最先执行

地址 = "你的DLL地址"  ' 请替换为实际的DLL地址

' 测试1: 调用文本函数 - 修复前的问题
测试文本函数 ()

' 测试2: 调用整数函数
测试整数函数 ()

' 测试3: 调用浮点函数
测试浮点函数 ()

' 测试4: 调用逻辑函数
测试逻辑函数 ()

.子程序 测试文本函数

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试文本函数 ===")

' 这是您原来的问题调用 - 现在应该能正确返回文本了！
调用成功 = 远程调用DLL (地址, "测试", "", 0, 结果, 3, 4096)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("文本结果: [" + 结果 + "]")

' 获取调试信息
获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试整数函数

.局部变量 结果, 整数型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试整数函数 ===")

调用成功 = 远程调用DLL (地址, "整数测试", "", 0, 结果, 1, 0)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("整数结果: " + 到文本 (结果))

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试浮点函数

.局部变量 结果, 小数型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试浮点函数 ===")

调用成功 = 远程调用DLL (地址, "浮点测试", "", 0, 结果, 2, 0)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("浮点结果: " + 到文本 (结果))

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试逻辑函数

.局部变量 结果, 逻辑型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试逻辑函数 ===")

调用成功 = 远程调用DLL (地址, "逻辑测试", "", 0, 结果, 4, 0)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("逻辑结果: " + 到文本 (结果))

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)

调试输出 ("")

.子程序 测试参数修复

.局部变量 结果, 文本型
.局部变量 调用成功, 整数型
.局部变量 调试信息, 文本型

调试输出 ("=== 测试参数修复 ===")

' 这是您遇到的具体问题：paramCount=1 但参数为空
调用成功 = 远程调用DLL (地址, "测试", "", 1, 结果, 2, 256)

调试输出 ("调用成功: " + 到文本 (调用成功))
调试输出 ("结果: [" + 结果 + "]")

获取调试信息 (调试信息, 1024)
调试输出 ("调试信息: " + 调试信息)
调试输出 ("注意: paramCount从1自动调整为0")

调试输出 ("")
